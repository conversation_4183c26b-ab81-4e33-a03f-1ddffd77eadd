/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "current": {
    "brand_name": "olukai",
    "wishlist_url": "/account?view=wishlist",
    "create_an_account_image": "shopify://shop_images/sign-bkgd.webp",
    "sign_in_image": "shopify://shop_images/sign-bkgd.webp",
    "login_text_color": "#381300",
    "login_title": "New to OluKai?",
    "login_subtitle": "Make it easy to check out faster, keep your order history organized, and be first to hear of new product launches.",
    "login_link_title": "Create Account",
    "login_url": "/account/register",
    "reset_image": "shopify://shop_images/sign-bkgd.webp",
    "rewards_callout_image": "shopify://shop_images/logo-hh_stacked-gold_25c657ef-09cb-42f7-8521-865918993e4f.webp",
    "rewards_callout_image_mobile": "shopify://shop_images/logo-hh_horizontal-gold_b1e4e947-c7b1-4855-aba1-97c4d5cac401.webp",
    "rewards_callout_message": "Join for free and earn Makau to unlock exclusive rewards and experiences. Plus, free shipping on all orders.",
    "favicon": "shopify://shop_images/olukai-makaiblue_transparent-310x310_2x_64x64_fceb0dc7-b67a-41ac-92c9-6cfdb36ecacd.png",
    "enable_custom_meta_title": true,
    "custom_meta_title": "🤙 Hey, There is Aloha In Here👇",
    "product_item_show_swatches": false,
    "product_item_swatch_interaction": "click",
    "product_item_enhanced_data": true,
    "product_item_enhanced_data_key": "502b9392060a09bb334c22138325839c",
    "product_color_option_type": "swatch_image",
    "peripherals_map": "{\n        archipelago:{\n            '2.properties.birthday':'profiles.0.birthday',\n\n'2.properties.email':'identity.email',\n            '2.properties.email':'profiles.0.email',\n\n'2.properties.phone':'identity.phone',           \n            '2.properties.phone':'profiles.0.phone',\n            \n            '2.properties.first_name':'identity.first_name',\n            '2.properties.first_name':'profiles.0.first_name',  \n            '2.properties.last_name':'identity.last_name',\n            '2.properties.last_name':'profiles.0.last_name',\n            '2.properties.favorite_colors':'profiles.0.preferences.favorite_colors',\n            '2.properties.favorite_interests':'profiles.0.preferences.favorite_interests',\n        \n            '2.properties.shoe_gender':'profiles.0.shoe_gender',\n            '2.properties.sandal_size':'profiles.0.sandal_size',\n            '2.properties.shoe_size':'profiles.0.shoe_size',\n            '2.properties.size':'profiles.0.size',\n            '2.properties.state':'profiles.0.state',\n            '2.properties.secondary_first_name':'profiles.1.first_name',  \n            '2.properties.secondary_last_name':'profiles.1.last_name',\n            '2.properties.secondary_favorite_style':'profiles.1.preferences.favorite_style',\n            '2.properties.secondary_interests_new':'profiles.1.preferences.favorite_interests',\n            '2.properties.secondary_gender':'profiles.1.gender',\n            '2.properties.secondary_sandal_size':'profiles.1.sandal_size',\n            '2.properties.secondary_shoe_size':'profiles.1.size',\n            '2.properties.secondary_size':'profiles.1.size',\n            '2.properties.loyalty_id':'loyalty.loyalty_id',\n            '2.properties.loyalty_status':'loyalty.loyalty_status',\n            '2.properties.pending_point_balance':'loyalty.pending_point_balance',\n            '2.properties.points_balance':'loyalty.points_balance',\n            '2.properties.points_till_next_tier':'loyalty.points_till_next_tier',\n            '2.properties.points_to_maintain_current_tier':'points_to_maintain_current_tier',\n            '2.properties.profile_completed':'profile_completed',\n            '2.properties.tier':'tier'\n        \n        }}",
    "mparticle_enable": true,
    "mparticle_domain_name": "olukai.com",
    "key_mparticle": "us2-9e6c401eaa22f941abea4741da638852",
    "is_development_mode": true,
    "logout_function": false,
    "mparticle_config": "v1SecureServiceUrl: 'mparticle.olukai.com/webevents/v1/JS/',\n        v2SecureServiceUrl: 'mparticle.olukai.com/webevents/v2/JS/',\n        v3SecureServiceUrl: 'mparticle.olukai.com/webevents/v3/JS/',\n        configUrl: 'mparticle.olukai.com/tags/JS/v2/',\n        identityUrl: 'mparticle.olukai.com/identity/v1/',\n        aliasUrl: 'mparticle.olukai.com/webevents/v1/identity/',",
    "klaviyo_new_registration_check": "document.addEventListener('DOMContentLoaded', function(event) {\n  if(localStorage.getItem('registration_form_submit') && window.location.href.indexOf('/challenge') == -1){\n    let registrationEmail = localStorage.getItem('registration_form_submit')\n    const common_email_source = 'Subscribe to Newsletter via Registration'\n    const common_subscription_list_id = 'XDgAjV'\n    const raw_payload = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': common_email_source,'profile': {'data': {'type': 'profile','attributes': {'email': registrationEmail}}}},'relationships': {'list': {'data': {'type': 'list','id': common_subscription_list_id}}}}});\n    const common_email_public_api = 'UNpuvd'\n    const requestHeaders = new Headers();requestHeaders.append('revision', '2024-02-15');requestHeaders.append('Content-Type', 'application/json');\n    const request_Options = {method: 'POST',headers: requestHeaders,body: raw_payload,redirect: 'follow'};\n    fetch('https://a.klaviyo.com/client/subscriptions/?company_id='+common_email_public_api, request_Options).then((response) => response.text()).catch((error) => console.error('error',error));\n\tlocalStorage.removeItem('registration_form_submit');\n  }\n  console.log(111)\n  console.log(localStorage.getItem('sms_registration_form_submit'))\n  console.log(window.location.href.indexOf('/challenge'))\n  if(localStorage.getItem('sms_registration_form_submit') && window.location.href.indexOf('/challenge') == -1){\n  console.log(222)\n\tconst phone_number = localStorage.getItem('sms_registration_form_submit');\n\tconst common_sms_custom_source = 'Subscribe to SMS via Registration'\n\tconst common_sms_public_api = 'UNpuvd'\n\tconst common_sms_list_id = 'WvTvt3'\n\n\tconst myHeaders = new Headers();myHeaders.append('revision', '2024-02-15');myHeaders.append('Content-Type', 'application/json');\n\tconst raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': common_sms_custom_source,'profile': {'data': {'type': 'profile','attributes': {'phone_number': phone_number}}}},'relationships': {'list': {'data': {'type': 'list','id': common_sms_list_id}}}}});\n\tconst requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};\n\tfetch('https://a.klaviyo.com/client/subscriptions/?company_id='+common_sms_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));\n\tlocalStorage.removeItem('sms_registration_form_submit');\n  }\n});",
    "reamaze_lightbox": false,
    "reamaze_sso_key": "",
    "reamaze_account": "",
    "reamaze_enableKb": "",
    "reamaze_gradient": true,
    "reamaze_shoutboxFacesMode": "",
    "reamaze_faces_image": "shopify://shop_images/512x512_LOGO_430x_6b21b60b-ba7a-42b9-84da-f53d3b2b24cd.webp",
    "reamaze_shoutboxHeaderLogo": false,
    "reamaze_faq": false,
    "reamaze_orders": false,
    "exponea": false,
    "exponea_cdt_api_url": "https://ai.olukai.com",
    "exponea_token_id": "614d6164-71c0-11e9-a986-0a580a2037f6",
    "exponea_experiments_enabled": false,
    "exponea_experiments_mode": "async",
    "cookie_compliance_enabled": true,
    "cookie_compliance_id": "9IYaT_bdX",
    "enable_accessibility_statement": true,
    "accessibility_statement_link": "https://olukai-store.myshopify.com/?oseid=cb5oJGWMuYdjBkMCYbXGufVD",
    "accessibility_statement_text": "Accessibility",
    "code_head_start": "",
    "code_head_end": "<!-- Google tag (gtag.js) -->\n<script async src=\"https://www.googletagmanager.com/gtag/js?id=AW-**********\"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag('js', new Date());\n  gtag('config', 'AW-**********');\n</script>\n<meta name=\"google-site-verification\" content=\"8gPJcQwvMC-yo4MVhHJncrap2VcsLOYIkTLVnhgwT8s\" />\n<script src=\"https://cdn.tapcart.com/webbridge-sdk/webbridge.umd.js\"></script>\n\n<!-- equalweb.com custom-button -->\n<script>\nwindow.interdeal = {\n hideBtn: true,\n    \"sitekey\": \"664ce67251625244da10222d67c3ec79\",\n    \"Position\": \"left\",\n    \"domains\": {\n        \"js\": \"https://cdn.equalweb.com/\",\n        \"acc\": \"https://access.equalweb.com/\"\n    },\n    \"Menulang\": \"EN\",\n    \"btnStyle\": {\n        \"vPosition\": [\n            \"80%\",\n            \"20%\"\n        ],\n        \"scale\": [\n            \"0.5\",\n            \"0.5\"\n        ],\n        \"color\": {\n            \"main\": \"#021327\",\n            \"second\": \"#ffffff\"\n        },\n        \"icon\": {\n            \"outline\": false,\n            \"type\": 1,\n            \"shape\": \"circle\"\n        }\n    }\n};\n(function(doc, head, body){\n    var coreCall             = doc.createElement('script');\n    coreCall.src             = interdeal.domains.js + 'core/5.0.13/accessibility.js';\n    coreCall.defer           = true;\n    coreCall.integrity       = 'sha512-pk3CeR0KGJu+GfK2x2ybTSZ1o1qfua6XW2PRAxMWOhC85M3+CanPYmvRp6BOiW0/riZjWGerRN7+JH4wEF0wJQ==';\n    coreCall.crossOrigin     = 'anonymous';\n    coreCall.setAttribute('data-cfasync', true );\n    body? body.appendChild(coreCall) : head.appendChild(coreCall);\n})(document, document.head, document.body);\n</script>\n<!-- /equalweb custom-button -->",
    "code_body_end": "<!-- Sierra -->\n<script type=\"module\" src=\"https://sierra.chat/agent/c764ElIBicJRk8QBikiumJ119VxXjdUD1ZrnUdX7tvo/embed\"></script>\n    <script>\n        // JavaScript code to extract URL parameter\n        function getParameterByName(name) {\n            name = name.replace(/[\\[\\]]/g, \"\\\\$&\");\n            let url = window.location.href;\n            let regex = new RegExp(\"[?&]\" + name + \"(=([^&#]*)|&|#|$)\");\n            let results = regex.exec(url);\n            if (!results) return null;\n            if (!results[2]) return '';\n            return decodeURIComponent(results[2].replace(/\\+/g, \" \"));\n        }\n\n        // Get the 'variable' parameter from the URL\n        let variable = getParameterByName('variable');\n        let brand = getParameterByName('brand');\n\n        \n        // Extract 'ra_id' value from the 'variable' parameter\n        let ra_id = null;\n        if (variable) {\n            let parts = variable.split(':');\n            if (parts[0] === 'ra_id') {\n                ra_id = parts[1];\n            }\n        }\n\n        // Define sierraConfig and update the ra_id value\n        var sierraConfig = {\n            variables: {\n                \"ra_id\": ra_id || '', // Use the extracted ra_id value or an empty string if not found\n                \"locale\": \"en-US\",\n                \"brand\": brand || 'olukai'             },\n            useVoiceInput: true,\n            onDisabled() {\n                window.Reamaze.popup()\n            }\n        };\n    </script>\n    <!-- /Sierra -->\n\n\n\n<!-- Begin: Northbeam pixel -->\n<script>(function(){var r;(e=r=r||{}).A=\"identify\",e.B=\"trackPageView\",e.C=\"fireEmailCaptureEvent\",e.D=\"fireCustomGoal\",e.E=\"firePurchaseEvent\";var e=\"//j.northbeam.io/ota-sp/0df77b0e-47bd-41bb-b36e-4561a91b91f5.js\";function t(e){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];a.push({fnName:e,args:n})}var a=[],n=((n={_q:a})[r.A]=function(e,n){return t(r.A,e,n)},n[r.B]=function(){return t(r.B)},n[r.C]=function(e,n){return t(r.C,e,n)},n[r.D]=function(e,n){return t(r.D,e,n)},n[r.E]=function(e){return t(r.E,e)},window.Northbeam=n,document.createElement(\"script\"));n.async=!0,n.src=e,document.head.appendChild(n);})()</script>\n<!-- End: Northbeam pixel -->\n<!-- Amazon -->\n<script async defer type='text/javascript'>\nwindow.onload = function(){var _pix = document.getElementById('_pix_id_034a5392-ba15-6a3d-f085-6137a18fe8d7');if (!_pix) { var protocol = '//'; var a = Math.random() * 1000000000000000000; _pix = document.createElement('iframe'); _pix.style.display = 'none'; _pix.setAttribute('src', protocol + 's.amazon-adsystem.com/iu3?d=generic&ex-fargs=%3Fid%3D034a5392-ba15-6a3d-f085-6137a18fe8d7%26type%3D54%26m%3D1&ex-fch=416613&ex-src=https://olukai.com&ex-hargs=v%3D1.0%3Bc%3D2338444490901%3Bp%3D034A5392-BA15-6A3D-F085-6137A18FE8D7' + '&cb=' + a); _pix.setAttribute('id','_pix_id_034a5392-ba15-6a3d-f085-6137a18fe8d7'); document.body.appendChild(_pix);}}\n</script>\n<noscript>\n<img height='1' width='1' border='0' alt='' src='https://s.amazon-adsystem.com/iui3?d=forester-did&ex-fargs=%3Fid%3D034a5392-ba15-6a3d-f085-6137a18fe8d7%26type%3D54%26m%3D1&ex-fch=416613&ex-src=https://olukai.com&ex-hargs=v%3D1.0%3Bc%3D2338444490901%3Bp%3D034A5392-BA15-6A3D-F085-6137A18FE8D7' />\n</noscript>\n<!-- /Amazon -->\n\n<!-- Reddit -->\n<script async defer>\n!function(w,d){if(!w.rdt){var p=w.rdt=function(){p.sendEvent?p.sendEvent.apply(p,arguments):p.callQueue.push(arguments)};p.callQueue=[];var t=d.createElement(\"script\");t.src=\"https://www.redditstatic.com/ads/pixel.js\",t.async=!0;var s=d.getElementsByTagName(\"script\")[0];s.parentNode.insertBefore(t,s)}}(window,document);rdt('init','t2_8jm74cifg', {\"optOut\":false,\"useDecimalCurrencyValues\":true,\"email\":\"{% if customer %}{{ customer.email }}{% endif %}\"});rdt('track', 'PageVisit');\n</script>\n<!-- DO NOT MODIFY UNLESS TO REPLACE A USER IDENTIFIER -->\n<!-- /Reddit -->\n\n<!-- Twitter -->\n<script>\n!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);\n},s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',\na=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');\ntwq('config','of0rd');\n</script>\n<!-- /Twitter -->\n\n<!-- Outbrain -->\n<script data-obct type=\"text/javascript\" async defer>\nwindow.outbrain_enabled = true;\n!function(_window, _document) {\n  var OB_ADV_ID = '{{ settings.outbrain_ob_adv_id }}';\n  if (_window.obApi) {\n    var toArray = function(object) {\n      return Object.prototype.toString.call(object) === '[object Array]' ? object : [object];\n    };\n    _window.obApi.marketerId = toArray(_window.obApi.marketerId).concat(toArray(OB_ADV_ID));\n    return;\n  }\n  var api = _window.obApi = function() {\n    api.dispatch ? api.dispatch.apply(api, arguments) : api.queue.push(arguments);\n  };\n  api.version = '1.1';\n  api.loaded = true;\n  api.marketerId = OB_ADV_ID;\n  api.queue = [];\n  var tag = _document.createElement('script');\n  tag.async = true;\n  tag.src = '//amplify.outbrain.com/cp/obtp.js';\n  tag.type = 'text/javascript';\n  var script = _document.getElementsByTagName('script')[0];\n  script.parentNode.insertBefore(tag, script);\n}(window, document);\nobApi('track', 'PAGE_VIEW');\n</script>\n<!-- /Outbrain -->\n\n<!-- Pinterest -->  \n<script type=\"text/javascript\" async defer>\n  !function(e){if(!window.pintrk){window.pintrk=function()\n  {window.pintrk.queue.push(Array.prototype.slice.call(arguments))};var\n  n=window.pintrk;n.queue=[],n.version=\"3.0\";var\n  t=document.createElement(\"script\");t.async=!0,t.src=e;var\n  r=document.getElementsByTagName(\"script\")[0];r.parentNode.insertBefore(t,r)}}\n  (\"https://s.pinimg.com/ct/core.js\");\n  pintrk('load', '2620695777953', {\n    em: 'f6ce07f0096b4d82343c89f968469d2618e1e3a3740e15cede7508b31360df91',\n  });\n  pintrk('page');\n</script>\n<noscript>\n  <img height=\"1\" width=\"1\" style=\"display:none;\" alt=\"\"\n  src=\"https://ct.pinterest.com/v3/?tid=2620695777953&noscript=1\" />\n</noscript>\n<!-- /Pinterest -->\n\n<!-- Cybba -->\n<script type=\"text/javascript\">!function(){if(!document.querySelector(\"[src*='76DB9CBF-8F31-01CA-98BB-A1E04DEDB940']\")){var e=document.createElement(\"script\");e.type=\"text/javascript\",e.async=!0,e.src=\"//www.rtb123.com/tags/76DB9CBF-8F31-01CA-98BB-A1E04DEDB940/btp.js\";var t=document.getElementsByTagName(\"head\")[0];t?t.appendChild(e,t):(t=document.getElementsByTagName(\"script\")[0]).parentNode.insertBefore(e,t)}}();</script>\n<!-- /Cybba -->\n\n<!-- Ambassador -->\n<script>\n  (function (u, n, i, v, e, r, s, a, l) { u[r] = {}; u[r].uid = '673439b9-be6e-46ed-bfc0-75da53bdc423'; u[r].m = ['identify', 'on', 'ready', 'track', 'getReferrerInfo']; u[r].queue = []; u[r].f = function(t) { return function() { var l = Array.prototype.slice.call(arguments); l.unshift(t); u[r].queue.push(l); return u[r].queue; }; }; for (var t = 0; t < u[r].m.length; t++) { l = u[r].m[t]; u[r][l] = u[r].f(l); } a = n.createElement(v); a.src = e + '/us-' + u[r].uid + '.js'; a.async = s; n.getElementsByTagName(i)[0].appendChild(a); })(window, document, 'head', 'script', 'https://cdn.getambassador.com', 'mbsy', true);\n</script>\n<!-- /Ambassador -->\n\n<!-- Loop -->\n<script type=\"text/javascript\" async defer>\n(function(){if(localStorage.getItem('_LoopSession') || document.location.search.includes('loop')){localStorage.setItem('_LoopSession', true);var s=document.createElement(\"script\");t.async=!0,t.src='https://cdn.jsdelivr.net/npm/@loophq/onstore-sdk@latest/dist/loop-onstore-sdk.min.js';var nr=document.getElementsByTagName(\"script\")[0];r.parentNode.insertBefore(t,r);setTimeout(()=>{LoopOnstore.init({ key: \"e35781788d8c1ee03929b3114816e39bd5502088\", attach: 'a[href*=\"/checkout\"]', });},10)\t}})();\n</script>\n<!-- /Loop -->\n\n<!-- Canella Pixel -->\n\n<img id = cannellamedia>\n<script src=https://df8nroy20256x.cloudfront.net/cannella_OluKai.js></script>\n\n<script>\nif(localStorage.getItem('canella_media_pixel')){\n\n  localStorage.setItem('canella_media_pixel',true);\n\n  localStorage.setItem('canella_media_pixel',false);\n\n}\nelse{\n\n  localStorage.setItem('canella_media_pixel',true);\n\n  localStorage.setItem('canella_media_pixel',false);\n\n}        \n</script>\n<script async src='https://tag.simpli.fi/sifitag/c1b4c2bb-fb23-4851-b471-4f667ce5bf55'></script>\n<!-- /Canella Pixel -->\n\n\n<!-- equalweb custom-button -->\n<script>\ndocument.addEventListener('DOMContentLoaded', function() {\n    var menuBtn = document.querySelector('[data-INDmenu-btn=\"true\"]');\n    \n    if (menuBtn) {  // Check if the element exists\n      menuBtn.addEventListener('click', function(event) {\n        event.preventDefault();  // Prevent the default anchor behavior\n        //alert(\"lkdfjlksd\")\n        window.interdeal.a11y.openMenu();  // Call the desired function\n      });\n    } else {\n      console.log('Menu button not found.');\n    }\n  });\n</script>\n<!-- /equalweb custom-button code end -->",
    "split_content_for_header": "",
    "block_scripts": "",
    "script_block_exceptions": "",
    "script_cart_add": "setTimeout(() => {\n    console.log('item added to cart ', e.detail);\n    try {\n        window.Northbeam.fireCustomGoal(\"add_to_cart\", {});\n        console.log('working');\n    } catch (error) {\n        console.error(\"An error occurred while firing the custom goal:\", error);\n    }\n}, 500);",
    "script_customer_identify": "console.log('customer identified',e.detail)",
    "fraud_filter_express_checkout_enabled": true,
    "checkout_sticky_cta_enabled": true,
    "address_validation_enable": true,
    "estimated_shipping_delay": "1",
    "blackout_dates": "5/25/20,7/4/20,9/7/20,11/26/20,12/25/20",
    "shipping_text_message": "We do not ship to P.O. Boxes. Selecting an expedited shipping method only expedites the transit time, it does not expedite the processing time. When your order ships, you will receive a tracking number to follow your shipment from our distribution center to your front door.",
    "estimated_text_space": "53",
    "shipping_text_loading_message": "Est. Arrival: Checking with the locals. BRB",
    "shipping_map": "{\n    \"shipping_map\": [\n        {\n            \"carrier\": \"ups\",\n            \"shipping_methods\": []\n        },\n        {\n            \"carrier\": \"other\",\n            \"shipping_methods\": [\n                {\n                    \"shopify_title\": \"Economy\",\n                    \"delay\": 6,\n                    \"delay_end\": 2,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Standard\",\n                    \"delay\": 3,\n                    \"delay_end\": 2,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Expedited\",\n                    \"delay\": 2,\n                    \"delay_end\": 1,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Express\",\n                    \"delay\": 1,\n                    \"delay_end\": 1,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Priority Mail\",\n                    \"delay\": 6,\n                    \"delay_end\": 2,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Irvine HQ\",\n                    \"delay\": 3,\n                    \"delay_end\": null,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Christmas Delivery\",\n                    \"delay\": null,\n                    \"delay_end\": null,\n                    \"date_show\": \"2021/12/24\",\n                    \"date_hide\": \"2021/12/22 11:00\"\n                },\n                {\n                    \"shopify_title\": \"melin Members Free Shipping\",\n                    \"delay\": 6,\n                    \"delay_end\": 2,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Outsider Members Free Shipping\",\n                    \"delay\": 6,\n                    \"delay_end\": 2,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                },\n                {\n                    \"shopify_title\": \"Outsider Members Hawaii Shipping\",\n                    \"delay\": 6,\n                    \"delay_end\": 2,\n                    \"date_show\": null,\n                    \"date_hide\": null\n                }\n            ]\n        }\n    ]\n}",
    "profile_split_enable": true,
    "twitter_pixel_enable": true,
    "twitter_pixel_code": "!function(e,t,n,s,u,a){e.twq||(s=e.twq=function(){s.exe?s.exe.apply(s,arguments):s.queue.push(arguments);\n              },s.version='1.1',s.queue=[],u=t.createElement(n),u.async=!0,u.src='https://static.ads-twitter.com/uwt.js',\n              a=t.getElementsByTagName(n)[0],a.parentNode.insertBefore(u,a))}(window,document,'script');\n              twq('config','of0rd');\n          // Insert Twitter Event ID\n          twq('event', 'tw-of0rd-of3iz', {\n            value: null // use this to pass the value of the conversion (e.g. 5.00)\n          });\n          twq('event', 'tw-of0rd-of3j1', {\n            value: null // use this to pass the value of the conversion (e.g. 5.00)\n          });",
    "canella_media_pixel": true,
    "canella_pixel_code": "<img id=\"cannellamedia\">\n        <script src=\"https://df8nroy20256x.cloudfront.net/cannella_OluKai.js\"></script>",
    "mntn_pixel_enable": true,
    "mntn_pixel_code": "<!--MNTN Tracking Pixel-->\n<!-- INSTALL ON ALL PAGES OF SITE-->\n<script type=\"text/javascript\">\n\t(function(){\"use strict\";var e=null,b=\"4.0.0\",\n\tn=\"35196\",\n\tadditional=\"term=value\",\n\tt,r,i;try{t=top.document.referer!==\"\"?encodeURIComponent(top.document.referrer.substring(0,2048)):\"\"}catch(o){t=document.referrer!==null?document.referrer.toString().substring(0,2048):\"\"}try{r=window&&window.top&&document.location&&window.top.location===document.location?document.location:window&&window.top&&window.top.location&&\"\"!==window.top.location?window.top.location:document.location}catch(u){r=document.location}try{i=parent.location.href!==\"\"?encodeURIComponent(parent.location.href.toString().substring(0,2048)):\"\"}catch(a){try{i=r!==null?encodeURIComponent(r.toString().substring(0,2048)):\"\"}catch(f){i=\"\"}}var l,c=document.createElement(\"script\"),h=null,p=document.getElementsByTagName(\"script\"),d=Number(p.length)-1,v=document.getElementsByTagName(\"script\")[d];if(typeof l===\"undefined\"){l=Math.floor(Math.random()*1e17)}h=\"dx.mountain.com/spx?\"+\"dxver=\"+b+\"&shaid=\"+n+\"&tdr=\"+t+\"&plh=\"+i+\"&cb=\"+l+additional;c.type=\"text/javascript\";c.src=(\"https:\"===document.location.protocol?\"https://\":\"http://\")+h;v.parentNode.insertBefore(c,v)})()\n</script>",
    "cybba_pixel_enable": true,
    "cybba_pixel_code": "<script type=\"text/javascript\">!function(){if(!document.querySelector(\"[src*='76DB9CBF-8F31-01CA-98BB-A1E04DEDB940']\")){var e=document.createElement(\"script\");e.type=\"text/javascript\",e.async=!0,e.src=\"//www.rtb123.com/tags/76DB9CBF-8F31-01CA-98BB-A1E04DEDB940/btp.js\";var t=document.getElementsByTagName(\"head\")[0];t?t.appendChild(e,t):(t=document.getElementsByTagName(\"script\")[0]).parentNode.insertBefore(e,t)}}();</script>",
    "gtm_container_id": "GTM-53WQJ2V",
    "outbrain_pixel_enabled": true,
    "sms_legal_consent_copy1": "Unlock EARLY access to our exclusive products when you sign up for texts.",
    "sms_legal_consent_copy2": "<p>By submitting this form, you agree to receive recurring automated and promotional marketing text messages (e.g. cart reminders) from OluKai at the cell number used when signing up. Reply HELP for help and STOP to cancel. Msg frequency varies. Msg & data rates apply. View <a href=\"/pages/messaging-terms-conditions\" target=\"_blank\" title=\"Messaging Terms & Conditions\">Terms</a> & <a href=\"/pages/messaging-privacy-policy\" target=\"_blank\" title=\"Messaging Privacy Policy\">Privacy</a>.</p>",
    "type_header_font": "sans-serif",
    "type_subheader_font": "sans-serif",
    "type_body_font": "sans-serif",
    "type_nav_font": "sans-serif",
    "sections": {
      "header": {
        "type": "header",
        "blocks": {
          "bar_yWXhMg": {
            "type": "bar",
            "settings": {
              "title": "⤷ Bar",
              "bar_class_role": "header-bar--main-promo",
              "bar_class_position": "",
              "bar_style_background_color": "#001630",
              "bar_style_color": "#ffffff",
              "transparency_paths": "",
              "bar_transparent_text_color": ""
            }
          },
          "852a9e33-7fb9-4e3c-9cbb-c4daa3d39d5f": {
            "type": "announcements",
            "settings": {
              "title": "⤷ Announcements",
              "column_class_width": "w-full",
              "column_class_width_desktop": "lg:w-full",
              "column_classes": "text-center",
              "interval": 5
            }
          },
          "7f22c1d9-fdd6-4736-bd3f-a0d1c07a0f36": {
            "type": "announcement",
            "settings": {
              "inclusion": "1",
              "inclusion_js": "",
              "text": "Free Standard Shipping for Orders $120+",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://olukai.reamaze.com/articles/what-is-your-shipping-policy-and-how-much-does-shipping-cost?_brand=olukai"
            }
          },
          "announcement_idANwT": {
            "type": "announcement",
            "settings": {
              "inclusion": "1",
              "inclusion_js": "",
              "text": "Free 3-Day Expedited Shipping for Orders $175+",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://olukai.reamaze.com/articles/what-is-your-shipping-policy-and-how-much-does-shipping-cost?_brand=olukai"
            }
          },
          "392bc17e-5c82-45c0-bf7d-fd87abdaee97": {
            "type": "announcement",
            "disabled": true,
            "settings": {
              "inclusion": "2",
              "inclusion_js": "",
              "text": "Free 30-Day Exchanges",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://support.olukai.com/kb/returns/what-is-your-return-or-exchange-policy"
            }
          },
          "announcement_deVpr9": {
            "type": "announcement",
            "settings": {
              "inclusion": "1",
              "inclusion_js": "",
              "text": "Extended Free Holiday Returns Thru January 26th, 2025",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "https://support.olukai.com/articles/extended-holiday-return-period"
            }
          },
          "announcement_94hmKt": {
            "type": "announcement",
            "settings": {
              "inclusion": "2",
              "inclusion_js": "",
              "text": "Pair-a-Day Giveaway - Click Here to Enter",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "shopify://pages/pair-a-day-giveaway"
            }
          },
          "63229b20-4b8e-47f6-892a-542016812c59": {
            "type": "announcement",
            "disabled": true,
            "settings": {
              "inclusion": "2",
              "inclusion_js": "",
              "text": "Every Purchase Gives Back To Hawai‘i",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": "shopify://pages/commitment"
            }
          },
          "ff7bd8c1-7807-46cb-9cfd-d82c7d37c6b3": {
            "type": "announcement",
            "disabled": true,
            "settings": {
              "inclusion": "1",
              "inclusion_js": "",
              "text": "Free Expedited Shipping - Use Code \"PRIME\"",
              "text_desktop": "",
              "svg": "",
              "image_size": "",
              "link": ""
            }
          },
          "2a2f7a02-f7c2-4d5c-b1cf-dd4c7a470b1d": {
            "type": "bar",
            "settings": {
              "title": "⤷ Bar",
              "bar_class_role": "header-bar--main",
              "bar_class_position": "sticky top-0",
              "bar_style_background_color": "#ffffff",
              "bar_style_color": "",
              "transparency_paths": "",
              "bar_transparent_text_color": "#ffffff"
            }
          },
          "766c9413-2966-44a4-b600-b7f4c58e5cff": {
            "type": "menu-toggle",
            "settings": {
              "column_classes": "lg:hidden",
              "onclick": "document.querySelector('.header-bar--main').classList.toggle('active');\ndocument.querySelector('.header-bar--main details').open=true;\ndocument.querySelector('.header-bar--main details').click();"
            }
          },
          "9770ae93-2b88-40da-ad55-45003629b88b": {
            "type": "menu",
            "settings": {
              "column_classes": "hidden group-active:flex lg:flex justify-center w-1/3 lg:justify-start",
              "classes_format": "flex-row",
              "menu": "main-menu-arch-gift-guide",
              "hide_mobile": "Gift Guide",
              "hide_desktop": "",
              "mega_menu_hover": true
            }
          },
          "dc210b9c-d0d2-402f-98dc-ec7e0178e9fe": {
            "type": "logo",
            "settings": {
              "column_classes": "flex w-1/3 items-center justify-center",
              "logo": "<svg class=\"logo\" xmlns=\"http://www.w3.org/2000/svg\" x=\"0px\" y=\"0px\" width=\"142px\" height=\"28px\" viewBox=\"0 0 607.1 125.02\"><defs><style>.cls-1{fill:#001630;}</style>\n</defs>\n<g id=\"Layer_2\" data-name=\"Layer 2\"><g id=\"Layer_1-2\" data-name=\"Layer 1\">\n\t<rect class=\"cls-1\" x=\"253.15\" y=\"2.25\" width=\"29.3\" height=\"120.19\"/>\n\t\t<path class=\"cls-1\" d=\"M183.3,0c-35.61,0-60.4,25.84-60.4,62.5S147.69,125,183.3,125c35.9,0,61-25.84,61-62.49S219.2,0,183.3,0Zm0,97.2c-18,0-30.8-14.27-30.8-34.55s12.77-34.86,30.8-34.86c18.47,0,31.39,14.43,31.39,34.86S201.77,97.2,183.3,97.2Z\"/><path class=\"cls-1\" d=\"M323,87.89v-53H293.72V95.7c0,18.33,9.76,28.84,25.09,28.84,8.56,0,15.32-3.75,17.87-9.61L345,95.7c-4.06,2.85-8.27,4.35-11.87,4.35C328.27,100.05,323,98,323,87.89Z\"/><path class=\"cls-1\" d=\"M536.94,44.5A33.53,33.53,0,0,0,512.61,34c-21.19,0-38.46,19.83-38.46,45.22s15.92,45.37,35.3,45.37c7.36,0,13.52-3.75,16.23-9.61l8.86-19.23a26.57,26.57,0,0,1-13.37,4.35c-9.77,0-17.43-9.61-17.43-21.93,0-10.67,7.21-18.93,16.38-18.93s16.82,8.41,16.82,18v45.22h29.3V34.85h-29.3Z\"/><rect class=\"cls-1\" x=\"577.81\" y=\"34.85\" width=\"29.3\" height=\"87.59\"/><rect class=\"cls-1\" x=\"577.81\" y=\"2.25\" width=\"29.3\" height=\"24.49\"/><polygon class=\"cls-1\" points=\"486.31 2.25 452.21 2.25 417.51 59.64 417.51 2.25 388.21 2.25 388.21 122.44 417.51 122.44 417.51 65.5 446.65 122.44 480.15 122.44 446.95 61.59 486.31 2.25\"/><rect class=\"cls-1\" x=\"347.35\" y=\"34.85\" width=\"29.3\" height=\"87.59\"/><path class=\"cls-1\" d=\"M46,108.9c-18.42,0-32.24-21.43-24.44-45.17C26.66,49.38,35.87,35.39,39.59,29c2.83-4.78,1.24-7.09,1.24-10.47,0-2.83,1.77-4.07,4.6-4.07h8.15c3.9,0,4.43,3,3,5.13-2.31,3-6.56,3.73-9.57,8.69C43.13,34.84,37.82,48,35.69,60c-2.83,15,3.19,28.69,13.64,29.58,11.69.89,17.36-11.52,17.36-25,0-6.55-2.13-14.35-4.07-19.49-.18-.35,0-.52.17-.35a35.43,35.43,0,0,1,11,11,44.91,44.91,0,0,1,6.73,19.13c0,.17-.18.35-.36.17-1.77-2.3-8-9.56-7.79-5.31C73.42,88.35,64.74,108.9,46,108.9ZM46.85,125C78,125,98.93,100.58,92.39,67.1l-6.2-31.54C82.12,15,74,2.26,52.17,2.26H41.53C19.75,2.26,10.89,15,7,35.56L1.15,67.1C-5.23,101.47,15.67,125,46.85,125Z\"/>\n\t</g></g>\n\n</svg>"
            }
          },
          "spacer_Er3Gdm": {
            "type": "spacer",
            "settings": {
              "column_classes": "ml-auto"
            }
          },
          "8a34200f-ab57-4d1a-b680-b1356e3dca84": {
            "type": "search",
            "settings": {
              "column_classes": "order-3 lg:order-1 w-full lg:w-auto",
              "input_focus": "this.closest('header').classList.remove('active'); Util.wait(100).then(() => Header.updateHeaderOffset());",
              "search_suggestions": "",
              "search_terms_animation_speed": 20,
              "search_terms_inbetween_distance": 20,
              "search_terms_position": 75,
              "search_terms_position_tablet": 90,
              "search_terms_position_mobile": 90,
              "search_terms_classes": "text-[16px] lg:text-[12px] text-inherit",
              "search_terms_color": "#000000",
              "search_terms_color_opacity": 50,
              "search_terms_animation_delay": 2000
            }
          },
          "6d98e9ce-6f74-4acf-8881-e952769aa5c5": {
            "type": "nav-tools",
            "settings": {
              "column_classes": "order-2 gap-sm",
              "button_classes": "",
              "search": false,
              "wishlist": false,
              "account": true,
              "geo": false,
              "cart": true,
              "icon_size": 24,
              "display": "icons",
              "greeting": "<b style=\"color:#000;\" class=\"inline-block ml-2 mr-2\">{% if customer %}Aloha{% if customer.first_name %}, {{ customer.first_name }}{% endif %}!{% else %}Sign In{% endif %}</b>"
            }
          },
          "01e5ae78-c7be-4b98-afb7-16f05c9258c5": {
            "type": "menu",
            "settings": {
              "column_classes": "hidden group-active:block",
              "classes_format": "flex-col",
              "menu": "mega-menu-footer-mobile",
              "hide_mobile": "",
              "hide_desktop": "",
              "mega_menu_hover": false
            }
          }
        },
        "block_order": [
          "bar_yWXhMg",
          "852a9e33-7fb9-4e3c-9cbb-c4daa3d39d5f",
          "7f22c1d9-fdd6-4736-bd3f-a0d1c07a0f36",
          "announcement_idANwT",
          "392bc17e-5c82-45c0-bf7d-fd87abdaee97",
          "announcement_deVpr9",
          "announcement_94hmKt",
          "63229b20-4b8e-47f6-892a-542016812c59",
          "ff7bd8c1-7807-46cb-9cfd-d82c7d37c6b3",
          "2a2f7a02-f7c2-4d5c-b1cf-dd4c7a470b1d",
          "766c9413-2966-44a4-b600-b7f4c58e5cff",
          "9770ae93-2b88-40da-ad55-45003629b88b",
          "dc210b9c-d0d2-402f-98dc-ec7e0178e9fe",
          "spacer_Er3Gdm",
          "8a34200f-ab57-4d1a-b680-b1356e3dca84",
          "6d98e9ce-6f74-4acf-8881-e952769aa5c5",
          "01e5ae78-c7be-4b98-afb7-16f05c9258c5"
        ],
        "custom_css": [
          "@media only screen and (max-width: 1024px) {.logo {width: 121px; height: 26px; }}"
        ],
        "settings": {
          "unwrap": true
        }
      },
      "footer": {
        "type": "footer",
        "blocks": {
          "43b46537-7741-484f-ab80-9097f4604966": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap"
            }
          },
          "44c27b5a-a24e-44b2-b593-b113f1d4d85d": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-2/3",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "",
              "article_class_vertical_padding_desktop": "lg:py-xl",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "lg:gap-3xl"
            }
          },
          "9b1b00cd-f2fe-41df-a432-80ab00d17cc6": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "get-olukai",
              "columns": 1,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "",
              "item_class_gap_desktop": "",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "0fee3780-e75b-4ae7-9fd0-324bfa80d48b": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "customer-service",
              "columns": 1,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "d5deb735-0c7a-4d99-a8ba-b7474d4a9e8b": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "our-company",
              "columns": 1,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "menu_item_QJrfK7": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-auto",
              "link_list": "referral",
              "columns": 1,
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "py-0",
              "menu_class_horizontal_padding": "px-0",
              "menu_class_gap": "gap-0",
              "menu_class_vertical_padding_desktop": "lg:py-0",
              "menu_class_horizontal_padding_desktop": "lg:px-0",
              "menu_class_gap_desktop": "lg:gap-0"
            }
          },
          "e76cdf5e-2f09-4126-89b7-61aeec827d45": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap"
            }
          },
          "frame_hb6bph": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-auto",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-xl",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-lg"
            }
          },
          "content_item_FCAzt4": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "<style>\n   @media only screen and (max-width: 1024px) {\n      @media only screen and (max-width: 1024px) {\n         .tapcart-container{\n            padding: 16px 0px;\n         }\n         .qr-code {\n            display: none;\n         }\n      }\n   }\n   .tapcart-title {\n      font-family: \"GTA-Medium\";\n   }\n   .tapcart-button {\n      display: flex;\n      align-items: center;\n      height: 36px;\n      width: 190px;\n      align-items: center;\n      border-radius: 9999px;\n      background-color: #021327;\n      color: #fff;\n      padding: 0px 20px;\n      font-size: .833rem;\n      gap: 10px;\n   }\n   .tapcart-button-container {\n      gap: 6px;\n   }\n   .qr-code {\n      display: none; \n      position: absolute; \n      bottom: 85%; \n      left: 0; \n      padding: 10px; \n      pointer-events: none;\n      background-color: white; \n      border: 1px solid #ccc; \n      border-radius: 8px; \n      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); \n      z-index: 10; \n      opacity: 0; \n      transition: opacity 0.25s ease-in-out; \n   }\n   .qr-code::after {\n      content: \"\";\n      position: absolute;\n      top: 100%; /* Position below the QR code */\n      left: 50%;\n      transform: translateX(-50%);\n      border-width: 20px;\n      border-style: solid;\n      border-color: #fff transparent transparent transparent;\n   }\n   .qr-code-playstore {\n      bottom: 50%\n   }\n   .qr-code.show {\n      display: block; \n      opacity: 1; \n   }\n   \n</style>\n<div class=\"flex flex-col\">\n   <hr class=\"lg:hidden\" style=\"border-top: 1px solid rgb(200 196 186);\">\n   <div class=\"flex flex-col justify-start text-left gap-4 tapcart-container\">\n      <span class=\"tapcart-title\">Download our App</span>\n      <div class=\"tapcart-button-container flex flex-col\">\n         <button class=\"tapcart-button appstore\">\n            <svg version=\"1.1\" role=\"presentation\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" x=\"0px\" y=\"0px\" width=\"14px\" height=\"18px\" viewbox=\"0 0 14 18\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" xml:space=\"preserve\">\n               <g fill=\"#ffffff\">\n               <path d=\"M11.693,9.54c-0.022-2.178,1.776-3.223,1.856-3.275c-1.01-1.477-2.584-1.68-3.145-1.703C9.067,4.426,7.792,5.351,7.113,5.351c-0.677,0-1.727-0.769-2.836-0.749C2.817,4.623,1.471,5.451,0.72,6.758c-1.516,2.63-0.387,6.528,1.089,8.663c0.723,1.043,1.584,2.217,2.714,2.174c1.089-0.043,1.5-0.705,2.817-0.705c1.316,0,1.687,0.705,2.839,0.683c1.171-0.021,1.914-1.064,2.63-2.111c0.829-1.212,1.171-2.386,1.191-2.445C13.974,13.005,11.717,12.14,11.693,9.54z\"></path>\n               <path d=\"M9.528,3.149c0.599-0.728,1.005-1.739,0.895-2.745C9.559,0.439,8.511,0.978,7.89,1.706C7.334,2.35,6.847,3.377,6.978,4.366C7.942,4.44,8.927,3.875,9.528,3.149z\"></path>\n               </g>\n            </svg>\n            ON THE APP STORE\n         </button>\n         <div class=\"qr-code\">\n            <div><img class=\"qr-code-image\" src=\"https://cdn.shopify.com/s/files/1/0015/9229/5523/files/qr.png?v=1728554921\"></div>\n         </div>\n         <button class=\"tapcart-button playstore\">\n            <svg version=\"1.1\" role=\"presentation\" aria-hidden=\"true\" xmlns=\"http://www.w3.org/2000/svg\" x=\"0px\" y=\"0px\" width=\"14px\" height=\"15px\" viewbox=\"0 0 14 15\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" xml:space=\"preserve\">\n               <g fill=\"#ffffff\">\n               <path d=\"M9.6,4.5L1.45,0.1C1.4,0.05,1.3,0.05,1.15,0L7.5,6.75L9.6,4.5z\"></path>\n               <path d=\"M13.45,6.6L10.5,5L8.15,7.5L10.5,10l2.95-1.6C13.8,8.2,14,7.9,14,7.5S13.8,6.75,13.45,6.6z\"></path>\n               <path d=\"M0.2,0.45C0.05,0.6,0,0.8,0,1v13c0,0.2,0.05,0.4,0.2,0.6l6.6-7.1L0.2,0.45z\"></path>\n               <path d=\"M1.15,15c0.1,0,0.2-0.05,0.3-0.1l8.15-4.4L7.5,8.25L1.15,15z\"></path>\n               </g>\n            </svg>\n            ON GOOGLE PLAY\n         </button>\n      </div>\n      <div class=\"qr-code qr-code-appstore\">\n         <div><img class=\"qr-code-image\" src=\"https://cdn.shopify.com/s/files/1/0015/9229/5523/files/qr.png?v=1728554921\"></div>\n      </div>\n      <div class=\"qr-code qr-code-playstore\">\n         <div><img class=\"qr-code-image\" src=\"https://cdn.shopify.com/s/files/1/0015/9229/5523/files/qr.png?v=1728554921\"></div>\n      </div>\n   </div>\n</div>\n<script>\n   function onAppStoreClick() {\n      window.location.href='https://apps.apple.com/us/app/olukai/id6702029222 ';\n   }\n   function onGooglePlayClick() {\n      window.location.href='https://play.google.com/store/apps/details?id=co.app.id_Q2RZBfbbFC&pli=1';\n   }\n   document.querySelector(\".tapcart-button:first-child\").addEventListener(\"click\", onAppStoreClick);\n   document.querySelector(\".tapcart-button:last-child\").addEventListener(\"click\", onGooglePlayClick);\n\n   const appStoreButton = document.querySelector('.tapcart-button.appstore');\n   const playStoreButton = document.querySelector('.tapcart-button.playstore');\n\n   function setupHoverEffects() {\n      if (window.innerWidth > 1024) {\n         appStoreButton.addEventListener('mouseenter', () => {\n            const qrCode = document.querySelector('.qr-code-appstore');\n            if (qrCode) {\n               qrCode.classList.add('show');\n            }\n         });\n\n         appStoreButton.addEventListener('mouseleave', () => {\n            const qrCode = document.querySelector('.qr-code-appstore');\n            if (qrCode) {\n               qrCode.classList.remove('show');\n            }\n         });\n\n         playStoreButton.addEventListener('mouseenter', () => {\n            const qrCode = document.querySelector('.qr-code-playstore');\n            if (qrCode) {\n               qrCode.classList.add('show');\n            }\n         });\n\n         playStoreButton.addEventListener('mouseleave', () => {\n            const qrCode = document.querySelector('.qr-code-playstore');\n            if (qrCode) {\n               qrCode.classList.remove('show');\n            }\n         });\n      }\n   }\n   setupHoverEffects();\n   window.addEventListener('resize', setupHoverEffects); \n\n   </script>",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "frame_DiddRY": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "max-lg:hidden",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "content_item_ELDt77": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "<a href=\"https://www.facebook.com/olukai/\" target=\"_blank\">\n<svg xmlns=\"http://www.w3.org/2000/svg\" shape-rendering=\"geometricPrecision\" text-rendering=\"geometricPrecision\" image-rendering=\"optimizeQuality\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" viewBox=\"0 0 509 509\" height=\"25px\" width=\"25px\"><g fill-rule=\"nonzero\"><path fill=\"#0866FF\" d=\"M509 254.5C509 113.94 395.06 0 254.5 0S0 113.94 0 254.5C0 373.86 82.17 474 193.02 501.51V332.27h-52.48V254.5h52.48v-33.51c0-86.63 39.2-126.78 124.24-126.78 16.13 0 43.95 3.17 55.33 6.33v70.5c-6.01-.63-16.44-.95-29.4-.95-41.73 0-57.86 15.81-57.86 56.91v27.5h83.13l-14.28 77.77h-68.85v174.87C411.35 491.92 509 384.62 509 254.5z\"/><path fill=\"#fff\" d=\"M354.18 332.27l14.28-77.77h-83.13V227c0-41.1 16.13-56.91 57.86-56.91 12.96 0 23.39.32 29.4.95v-70.5c-11.38-3.16-39.2-6.33-55.33-6.33-85.04 0-124.24 40.16-124.24 126.78v33.51h-52.48v77.77h52.48v169.24c19.69 4.88 40.28 7.49 61.48 7.49 10.44 0 20.72-.64 30.83-1.86V332.27h68.85z\"/></g></svg>\n</a>",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "content_item_8KmfYk": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "<a href=\"https://x.com/olukai\" target=\"_blank\">\n<svg xmlns=\"http://www.w3.org/2000/svg\" shape-rendering=\"geometricPrecision\" text-rendering=\"geometricPrecision\" image-rendering=\"optimizeQuality\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" viewBox=\"0 0 512 509.64\" height=\"25px\" width=\"25px\"><rect width=\"512\" height=\"509.64\" rx=\"115.61\" ry=\"115.61\"/><path fill=\"#fff\" fill-rule=\"nonzero\" d=\"M323.74 148.35h36.12l-78.91 90.2 92.83 122.73h-72.69l-56.93-74.43-65.15 74.43h-36.14l84.4-96.47-89.05-116.46h74.53l51.46 68.04 59.53-68.04zm-12.68 191.31h20.02l-129.2-170.82H180.4l130.66 170.82z\"/></svg>\n</a>",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "content_item_bAXXKe": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "<a href=\"https://www.instagram.com/olukai/\" target=\"_blank\">\n    <svg xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" viewBox=\"0 0 132.004 132\" height=\"25px\" width=\"25px\"><defs><linearGradient id=\"b\"><stop offset=\"0\" stop-color=\"#3771c8\"/><stop stop-color=\"#3771c8\" offset=\".128\"/><stop offset=\"1\" stop-color=\"#60f\" stop-opacity=\"0\"/></linearGradient><linearGradient id=\"a\"><stop offset=\"0\" stop-color=\"#fd5\"/><stop offset=\".1\" stop-color=\"#fd5\"/><stop offset=\".5\" stop-color=\"#ff543e\"/><stop offset=\"1\" stop-color=\"#c837ab\"/></linearGradient><radialGradient id=\"c\" cx=\"158.429\" cy=\"578.088\" r=\"65\" xlink:href=\"#a\" gradientUnits=\"userSpaceOnUse\" gradientTransform=\"matrix(0 -1.98198 1.8439 0 -1031.402 454.004)\" fx=\"158.429\" fy=\"578.088\"/><radialGradient id=\"d\" cx=\"147.694\" cy=\"473.455\" r=\"65\" xlink:href=\"#b\" gradientUnits=\"userSpaceOnUse\" gradientTransform=\"matrix(.17394 .86872 -3.5818 .71718 1648.348 -458.493)\" fx=\"147.694\" fy=\"473.455\"/></defs><path fill=\"url(#c)\" d=\"M65.03 0C37.888 0 29.95.028 28.407.156c-5.57.463-9.036 1.34-12.812 3.22-2.91 1.445-5.205 3.12-7.47 5.468C4 13.126 1.5 18.394.595 24.656c-.44 3.04-.568 3.66-.594 19.188-.01 5.176 0 11.988 0 21.125 0 27.12.03 35.05.16 36.59.45 5.42 1.3 8.83 3.1 12.56 3.44 7.14 10.01 12.5 17.75 14.5 2.68.69 5.64 1.07 9.44 1.25 1.61.07 18.02.12 34.44.12 16.42 0 32.84-.02 34.41-.1 4.4-.207 6.955-.55 9.78-1.28 7.79-2.01 14.24-7.29 17.75-14.53 1.765-3.64 2.66-7.18 3.065-12.317.088-1.12.125-18.977.125-36.81 0-17.836-.04-35.66-.128-36.78-.41-5.22-1.305-8.73-3.127-12.44-1.495-3.037-3.155-5.305-5.565-7.624C116.9 4 111.64 1.5 105.372.596 102.335.157 101.73.027 86.19 0H65.03z\" transform=\"translate(1.004 1)\"/><path fill=\"url(#d)\" d=\"M65.03 0C37.888 0 29.95.028 28.407.156c-5.57.463-9.036 1.34-12.812 3.22-2.91 1.445-5.205 3.12-7.47 5.468C4 13.126 1.5 18.394.595 24.656c-.44 3.04-.568 3.66-.594 19.188-.01 5.176 0 11.988 0 21.125 0 27.12.03 35.05.16 36.59.45 5.42 1.3 8.83 3.1 12.56 3.44 7.14 10.01 12.5 17.75 14.5 2.68.69 5.64 1.07 9.44 1.25 1.61.07 18.02.12 34.44.12 16.42 0 32.84-.02 34.41-.1 4.4-.207 6.955-.55 9.78-1.28 7.79-2.01 14.24-7.29 17.75-14.53 1.765-3.64 2.66-7.18 3.065-12.317.088-1.12.125-18.977.125-36.81 0-17.836-.04-35.66-.128-36.78-.41-5.22-1.305-8.73-3.127-12.44-1.495-3.037-3.155-5.305-5.565-7.624C116.9 4 111.64 1.5 105.372.596 102.335.157 101.73.027 86.19 0H65.03z\" transform=\"translate(1.004 1)\"/><path fill=\"#fff\" d=\"M66.004 18c-13.036 0-14.672.057-19.792.29-5.11.234-8.598 1.043-11.65 2.23-3.157 1.226-5.835 2.866-8.503 5.535-2.67 2.668-4.31 5.346-5.54 8.502-1.19 3.053-2 6.542-2.23 11.65C18.06 51.327 18 52.964 18 66s.058 14.667.29 19.787c.235 5.11 1.044 8.598 2.23 11.65 1.227 3.157 2.867 5.835 5.536 8.503 2.667 2.67 5.345 4.314 8.5 5.54 3.054 1.187 6.543 1.996 11.652 2.23 5.12.233 6.755.29 19.79.29 13.037 0 14.668-.057 19.788-.29 5.11-.234 8.602-1.043 11.656-2.23 3.156-1.226 5.83-2.87 8.497-5.54 2.67-2.668 4.31-5.346 5.54-8.502 1.18-3.053 1.99-6.542 2.23-11.65.23-5.12.29-6.752.29-19.788 0-13.036-.06-14.672-.29-19.792-.24-5.11-1.05-8.598-2.23-11.65-1.23-3.157-2.87-5.835-5.54-8.503-2.67-2.67-5.34-4.31-8.5-5.535-3.06-1.187-6.55-1.996-11.66-2.23-5.12-.233-6.75-.29-19.79-.29zm-4.306 8.65c1.278-.002 2.704 0 4.306 0 12.816 0 14.335.046 19.396.276 4.68.214 7.22.996 8.912 1.653 2.24.87 3.837 1.91 5.516 3.59 1.68 1.68 2.72 3.28 3.592 5.52.657 1.69 1.44 4.23 1.653 8.91.23 5.06.28 6.58.28 19.39s-.05 14.33-.28 19.39c-.214 4.68-.996 7.22-1.653 8.91-.87 2.24-1.912 3.835-3.592 5.514-1.68 1.68-3.275 2.72-5.516 3.59-1.69.66-4.232 1.44-8.912 1.654-5.06.23-6.58.28-19.396.28-12.817 0-14.336-.05-19.396-.28-4.68-.216-7.22-.998-8.913-1.655-2.24-.87-3.84-1.91-5.52-3.59-1.68-1.68-2.72-3.276-3.592-5.517-.657-1.69-1.44-4.23-1.653-8.91-.23-5.06-.276-6.58-.276-19.398s.046-14.33.276-19.39c.214-4.68.996-7.22 1.653-8.912.87-2.24 1.912-3.84 3.592-5.52 1.68-1.68 3.28-2.72 5.52-3.592 1.692-.66 4.233-1.44 8.913-1.655 4.428-.2 6.144-.26 15.09-.27zm29.928 7.97c-3.18 0-5.76 2.577-5.76 5.758 0 3.18 2.58 5.76 5.76 5.76 3.18 0 5.76-2.58 5.76-5.76 0-3.18-2.58-5.76-5.76-5.76zm-25.622 6.73c-13.613 0-24.65 11.037-24.65 24.65 0 13.613 11.037 24.645 24.65 24.645C79.617 90.645 90.65 79.613 90.65 66S79.616 41.35 66.003 41.35zm0 8.65c8.836 0 16 7.163 16 16 0 8.836-7.164 16-16 16-8.837 0-16-7.164-16-16 0-8.837 7.163-16 16-16z\"/></svg>\n    </a>",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "content_item_eyHRxc": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "<a href=\"https://www.youtube.com/@OluKai\" target=\"_blank\">\n<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 333333 333333\" shape-rendering=\"geometricPrecision\" text-rendering=\"geometricPrecision\" image-rendering=\"optimizeQuality\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" height=\"25px\" width=\"25px\"><path d=\"M329930 100020s-3254-22976-13269-33065c-12691-13269-26901-13354-33397-14124-46609-3396-116614-3396-116614-3396h-122s-69973 0-116608 3396c-6522 793-20712 848-33397 14124C6501 77044 3316 100020 3316 100020S-1 126982-1 154001v25265c0 26962 3315 53979 3315 53979s3254 22976 13207 33082c12685 13269 29356 12838 36798 14254 26685 2547 113354 3315 113354 3315s70065-124 116675-3457c6522-770 20706-848 33397-14124 10021-10089 13269-33090 13269-33090s3319-26962 3319-53979v-25263c-67-26962-3384-53979-3384-53979l-18 18-2-2zM132123 209917v-93681l90046 46997-90046 46684z\" fill=\"red\"/></svg>\n</a>",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "break_DgWgt3": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "break_9yWgtV": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          },
          "da563e74-44b7-4187-b39b-f9affa88e360": {
            "type": "frame",
            "settings": {
              "article_class_visibility": "max-lg:hidden",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-auto",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "lg:py-xl",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "lg:gap-2xl"
            }
          },
          "content_item_Bhqz7V": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-auto",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-xs",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "#attr(data-acsb-custom-trigger:true)",
              "liquid_link": "",
              "image": "shopify://shop_images/accessibi.svg",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-center layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-lg",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-0",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-0",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-center layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "div",
              "text_item_1_class_type_style": "",
              "text_item_1_class_type_size": "type--sm",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "<div style=\"font-size:8px; font-weight:700; line-height:2;color:#001630;\">Accessibility</div>",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--primary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "60bacaa9-1c66-4e08-a5db-5d3484d51832": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-auto",
              "item_class_width_desktop": "lg:w-full",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "",
              "item_class_horizontal_padding": "",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "",
              "item_class_horizontal_padding_desktop": "",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image": "shopify://shop_images/b-certifed.svg",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-row",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "content_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "",
              "title_image_class_width": "w-full",
              "title_image_class_width_desktop": "lg:w-full",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "@include TypeStyle",
              "text_item_1_class_type_size": "@include TypeSize",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "@include ButtonStyle",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "break_8jLEzx": {
            "type": "break",
            "settings": {
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-0",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-0"
            }
          }
        },
        "block_order": [
          "43b46537-7741-484f-ab80-9097f4604966",
          "44c27b5a-a24e-44b2-b593-b113f1d4d85d",
          "9b1b00cd-f2fe-41df-a432-80ab00d17cc6",
          "0fee3780-e75b-4ae7-9fd0-324bfa80d48b",
          "d5deb735-0c7a-4d99-a8ba-b7474d4a9e8b",
          "menu_item_QJrfK7",
          "e76cdf5e-2f09-4126-89b7-61aeec827d45",
          "frame_hb6bph",
          "content_item_FCAzt4",
          "frame_DiddRY",
          "content_item_ELDt77",
          "content_item_8KmfYk",
          "content_item_bAXXKe",
          "content_item_eyHRxc",
          "break_DgWgt3",
          "break_9yWgtV",
          "da563e74-44b7-4187-b39b-f9affa88e360",
          "content_item_Bhqz7V",
          "60bacaa9-1c66-4e08-a5db-5d3484d51832",
          "break_8jLEzx"
        ],
        "custom_css": [
          ".content-item {display: flex;}"
        ],
        "settings": {
          "wrapper_style_background_color": "#eeece1",
          "wrapper_style_background": "",
          "wrapper_class_vertical_padding": "",
          "wrapper_class_horizontal_padding": "px-md",
          "wrapper_class_vertical_padding_desktop": "lg:py-xl",
          "wrapper_class_horizontal_padding_desktop": "lg:px-4xl",
          "container_class_container": "w-full",
          "container_class_vertical_padding": "py-0",
          "container_class_horizontal_padding": "px-0",
          "container_class_vertical_padding_desktop": "lg:py-0",
          "container_class_horizontal_padding_desktop": "lg:px-0",
          "credits": "<div class=\"mb-4 lg:hidden flex gap-xl\">\n\t<div class=\"lg:hidden flex justify-center items-center\">\n\t\t<svg version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\" x=\"0px\"\n\t\t\ty=\"0px\" width=\"50px\" height=\"82px\" viewBox=\"0 0 50 82\" style=\"enable-background:new 0 0 50 82;\"\n\t\t\txml:space=\"preserve\">\n\t\t\t<g fill=\"#021327\">\n\t\t\t\t<path\n\t\t\t\t\td=\"M39.901,9.134c0.878,0.05,1.731-0.297,2.327-0.944c0.317-0.317,0.527-0.726,0.6-1.169h-1.727c-0.075,0.167-0.182,0.318-0.315,0.445c-0.234,0.205-0.538,0.313-0.849,0.3c-0.299,0.009-0.594-0.076-0.843-0.243c-0.415-0.327-0.643-0.836-0.61-1.363h4.428c0.014-0.428-0.004-0.856-0.053-1.282c-0.064-0.476-0.232-0.932-0.493-1.335c-0.257-0.414-0.628-0.746-1.068-0.956c-0.456-0.206-0.953-0.309-1.454-0.3l-0.002-0.005c-0.83-0.033-1.635,0.29-2.212,0.888c-0.609,0.715-0.915,1.639-0.852,2.576c-0.091,0.961,0.256,1.911,0.944,2.588C38.328,8.857,39.102,9.142,39.901,9.134z M38.934,4.051c0.233-0.253,0.567-0.388,0.911-0.368l-0.002-0.005c0.337-0.007,0.663,0.12,0.906,0.355c0.262,0.274,0.407,0.639,0.407,1.018h-2.628C38.56,4.685,38.701,4.336,38.934,4.051z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M5.558,9.171c0.918,0.034,1.814-0.288,2.5-0.9c0.66-0.613,1.089-1.435,1.215-2.327h-1.81C7.38,6.311,7.222,6.657,7,6.96C6.639,7.391,6.094,7.624,5.532,7.589C4.931,7.6,4.359,7.328,3.987,6.856C3.535,6.213,3.32,5.434,3.379,4.651C3.329,3.85,3.531,3.053,3.955,2.372c0.353-0.518,0.946-0.82,1.573-0.8c0.553-0.04,1.095,0.169,1.478,0.57c0.219,0.28,0.375,0.604,0.457,0.95H9.29C9.246,2.501,9.036,1.936,8.685,1.46C7.927,0.457,6.709-0.089,5.456,0.012C4.44-0.022,3.456,0.369,2.739,1.091C1.869,2.053,1.425,3.327,1.51,4.622C1.441,5.829,1.826,7.019,2.59,7.956C3.349,8.783,4.437,9.228,5.558,9.171z\">\n\t\t\t\t</path>\n\t\t\t\t<rect x=\"33.879\" y=\"2.461\" width=\"1.716\" height=\"6.469\"></rect>\n\t\t\t\t<rect x=\"26.332\" y=\"0.124\" width=\"1.716\" height=\"1.561\"></rect>\n\t\t\t\t<path\n\t\t\t\t\td=\"M19.094,5.829c-0.022-0.394,0.046-0.787,0.2-1.151c0.286-0.473,0.823-0.733,1.371-0.665c0.05,0,0.117,0.002,0.2,0.006c0.083,0.004,0.178,0.012,0.285,0.024V2.311l-0.145-0.009c-0.022,0-0.049,0-0.08,0h-0.001c-0.445-0.021-0.882,0.119-1.233,0.394c-0.273,0.257-0.503,0.555-0.683,0.884V2.451h-1.614V8.92h1.7V5.829z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M22.438,7.708c-0.028,0.326,0.075,0.649,0.285,0.9c0.466,0.356,1.058,0.506,1.638,0.415l0.8-0.03V7.729c-0.055,0.006-0.112,0.009-0.169,0.009h-0.163c-0.211,0.026-0.425-0.008-0.617-0.1c-0.089-0.15-0.124-0.327-0.1-0.5V3.72h1.05v-1.2h-1.051v-1.8h-1.674v1.8h-0.9v1.2l0.901-0.001V7.708z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M30.025,8.927h1.687V3.711h1.11V2.516h-1.11V1.97c0-0.127,0.046-0.25,0.128-0.347c0.123-0.119,0.294-0.173,0.463-0.148c0.224,0.006,0.383,0.015,0.478,0.027V0.125l-0.356-0.027c-0.087-0.006-0.184-0.009-0.291-0.009l-0.002-0.001c-0.937,0-1.529,0.193-1.775,0.579c-0.278,0.422-0.402,0.927-0.35,1.43v0.415h-0.926v1.2h0.944V8.927z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M13.206,9.134c0.878,0.05,1.731-0.297,2.327-0.944c0.317-0.317,0.527-0.726,0.6-1.169h-1.727c-0.075,0.167-0.182,0.318-0.315,0.445c-0.234,0.205-0.538,0.313-0.849,0.3c-0.299,0.009-0.594-0.076-0.843-0.243c-0.418-0.326-0.649-0.838-0.616-1.368h4.427c0.014-0.428-0.004-0.856-0.053-1.282c-0.064-0.476-0.232-0.932-0.493-1.335c-0.257-0.414-0.628-0.746-1.068-0.956c-0.456-0.206-0.953-0.309-1.454-0.3h-0.001c-0.828-0.031-1.63,0.292-2.206,0.888c-0.609,0.715-0.915,1.639-0.852,2.576c-0.091,0.961,0.256,1.911,0.944,2.588C11.633,8.857,12.407,9.142,13.206,9.134z M12.232,4.046c0.233-0.253,0.567-0.388,0.91-0.368c0.336-0.008,0.662,0.117,0.905,0.35c0.262,0.274,0.407,0.639,0.407,1.018h-2.629C11.858,4.68,11.999,4.331,12.232,4.046z\">\n\t\t\t\t</path>\n\t\t\t\t<rect x=\"26.333\" y=\"2.456\" width=\"1.716\" height=\"6.469\"></rect>\n\t\t\t\t<rect x=\"33.878\" y=\"0.128\" width=\"1.717\" height=\"1.561\"></rect>\n\t\t\t\t<path\n\t\t\t\t\td=\"M48.285,0.194v3.085c-0.183-0.297-0.438-0.543-0.742-0.715c-0.32-0.176-0.68-0.265-1.045-0.258l-0.006-0.005c-0.791-0.027-1.548,0.326-2.037,0.949c-0.547,0.755-0.816,1.675-0.763,2.605c-0.035,0.853,0.241,1.69,0.777,2.355c0.46,0.585,1.165,0.924,1.909,0.917c0.4,0.014,0.796-0.076,1.151-0.261c0.34-0.206,0.624-0.493,0.825-0.837v0.9H50V0.194H48.285z M47.958,7.172c-0.247,0.355-0.66,0.557-1.092,0.534l-0.002-0.004c-0.427,0.025-0.836-0.18-1.072-0.537c-0.254-0.433-0.376-0.93-0.351-1.432c-0.017-0.487,0.103-0.97,0.347-1.392c0.231-0.374,0.65-0.59,1.089-0.561c0.313-0.004,0.617,0.106,0.855,0.309c0.443,0.449,0.663,1.072,0.6,1.7C48.354,6.277,48.223,6.761,47.958,7.172z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M33.512,79.166c-0.006-0.211-0.009-0.391-0.009-0.54v-2.143c0.05-0.469-0.186-0.922-0.6-1.149c-0.424-0.207-0.891-0.31-1.363-0.3c-0.62-0.065-1.235,0.163-1.663,0.617c-0.214,0.293-0.333,0.644-0.343,1.007h1.179c0.018-0.156,0.077-0.303,0.171-0.429c0.162-0.159,0.387-0.238,0.613-0.214c0.201-0.012,0.401,0.025,0.584,0.108c0.139,0.082,0.218,0.239,0.2,0.4c-0.002,0.16-0.108,0.301-0.261,0.347c-0.156,0.057-0.319,0.093-0.484,0.107l-0.416,0.045c-0.373,0.031-0.737,0.133-1.072,0.3c-0.441,0.255-0.699,0.738-0.664,1.247c-0.023,0.387,0.129,0.764,0.414,1.027c0.292,0.247,0.666,0.376,1.048,0.362c0.312,0.005,0.62-0.073,0.892-0.227c0.216-0.126,0.413-0.283,0.583-0.467c0.009,0.106,0.02,0.2,0.034,0.283c0.017,0.091,0.042,0.18,0.077,0.266h1.333v-0.18c-0.071-0.03-0.133-0.079-0.18-0.141C33.534,79.391,33.509,79.279,33.512,79.166z M32.278,78.002c0.03,0.321-0.099,0.636-0.345,0.844c-0.212,0.151-0.466,0.232-0.727,0.231c-0.153,0.004-0.302-0.045-0.422-0.139c-0.125-0.115-0.19-0.282-0.174-0.452c-0.012-0.213,0.098-0.414,0.283-0.519c0.174-0.086,0.361-0.142,0.553-0.167l0.274-0.051c0.11-0.019,0.219-0.047,0.324-0.084c0.082-0.032,0.16-0.071,0.234-0.118V78.002z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M36.009,73.891H34.8v1.3h-0.652v0.87H34.8v2.878c-0.019,0.234,0.055,0.467,0.206,0.647c0.337,0.257,0.764,0.366,1.183,0.3l0.579-0.021v-0.913l-0.122,0.006h-0.118c-0.152,0.02-0.307-0.005-0.446-0.071c-0.064-0.108-0.09-0.234-0.073-0.358v-2.469h0.759v-0.87h-0.759V73.891z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M24.733,75.74c-0.448-0.519-1.116-0.794-1.8-0.742c-0.684-0.053-1.352,0.223-1.8,0.742c-0.396,0.497-0.605,1.117-0.592,1.753c-0.016,0.638,0.193,1.261,0.592,1.76c0.452,0.513,1.118,0.784,1.8,0.731c0.681,0.053,1.348-0.218,1.8-0.731c0.398-0.498,0.608-1.122,0.592-1.76h-0.001C25.337,76.857,25.128,76.237,24.733,75.74z M23.756,78.576c-0.194,0.256-0.502,0.399-0.823,0.382h-0.001c-0.321,0.017-0.629-0.126-0.824-0.381c-0.212-0.32-0.313-0.701-0.289-1.085c-0.025-0.383,0.077-0.763,0.289-1.083c0.196-0.254,0.504-0.396,0.825-0.379c0.32-0.017,0.628,0.124,0.823,0.379c0.211,0.32,0.311,0.7,0.287,1.082C24.067,77.875,23.966,78.255,23.756,78.576z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M28.779,75.036c-0.322-0.014-0.638,0.087-0.891,0.287c-0.197,0.186-0.363,0.401-0.493,0.639v-0.814h-1.166v4.667h1.23v-2.227c-0.017-0.284,0.031-0.569,0.141-0.832c0.207-0.341,0.594-0.529,0.99-0.48c0.037-0.003,0.086-0.003,0.146,0c0.06,0.003,0.129,0.008,0.206,0.017v-1.252l-0.105-0.006H28.779z\">\n\t\t\t\t</path>\n\t\t\t\t<rect x=\"37.61\" y=\"75.148\" width=\"1.237\" height=\"4.667\"></rect>\n\t\t\t\t<path\n\t\t\t\t\td=\"M48.071,75.036c-0.341-0.015-0.677,0.084-0.957,0.28c-0.176,0.148-0.327,0.325-0.445,0.523v-0.682h-1.179v4.658h1.216v-2.525c-0.008-0.243,0.033-0.485,0.12-0.712c0.14-0.342,0.484-0.555,0.853-0.527c0.281-0.031,0.552,0.112,0.686,0.36c0.075,0.179,0.109,0.372,0.1,0.566v2.837h1.249v-3.141c0.047-0.467-0.122-0.931-0.458-1.259C48.919,75.152,48.499,75.017,48.071,75.036z\">\n\t\t\t\t</path>\n\t\t\t\t<rect x=\"37.611\" y=\"73.463\" width=\"1.237\" height=\"1.128\"></rect>\n\t\t\t\t<path\n\t\t\t\t\td=\"M42.169,74.998c-0.684-0.053-1.352,0.223-1.8,0.742c-0.396,0.497-0.605,1.117-0.592,1.753c-0.016,0.638,0.193,1.261,0.592,1.76c0.452,0.513,1.118,0.784,1.8,0.731c0.681,0.053,1.348-0.218,1.8-0.731c0.398-0.498,0.608-1.122,0.592-1.76h0.003c0.012-0.636-0.198-1.256-0.595-1.753C43.521,75.221,42.853,74.945,42.169,74.998z M42.992,78.576c-0.194,0.255-0.501,0.398-0.821,0.382c-0.322,0.017-0.631-0.126-0.826-0.382c-0.212-0.321-0.313-0.701-0.289-1.085c-0.024-0.382,0.077-0.762,0.289-1.082c0.196-0.254,0.504-0.396,0.825-0.379c0.32-0.017,0.628,0.124,0.823,0.379c0.211,0.32,0.311,0.7,0.287,1.082C43.303,77.875,43.203,78.255,42.992,78.576z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M2.909,74.504c0.399-0.029,0.79,0.122,1.067,0.412c0.159,0.204,0.272,0.439,0.331,0.691h1.316c-0.03-0.426-0.18-0.835-0.433-1.179c-0.548-0.724-1.428-1.117-2.333-1.043c-0.734-0.024-1.445,0.258-1.963,0.78c-0.628,0.695-0.948,1.615-0.887,2.551c-0.049,0.871,0.229,1.729,0.78,2.406c0.549,0.595,1.334,0.915,2.143,0.874c0.663,0.026,1.311-0.206,1.807-0.647c0.476-0.443,0.786-1.036,0.877-1.68H4.306c-0.06,0.265-0.173,0.514-0.334,0.733c-0.261,0.311-0.654,0.479-1.059,0.454c-0.435,0.007-0.849-0.191-1.117-0.534c-0.326-0.464-0.481-1.027-0.439-1.593c-0.035-0.578,0.11-1.152,0.416-1.644C2.027,74.709,2.456,74.49,2.909,74.504z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M8.662,74.998c-0.684-0.053-1.352,0.223-1.8,0.742c-0.396,0.497-0.605,1.117-0.592,1.753c-0.016,0.638,0.193,1.261,0.592,1.76c0.452,0.513,1.118,0.784,1.8,0.731c0.681,0.053,1.348-0.218,1.8-0.731c0.398-0.498,0.608-1.122,0.592-1.76c0.013-0.635-0.196-1.255-0.592-1.753C10.013,75.221,9.345,74.945,8.662,74.998z M9.485,78.576c-0.194,0.256-0.502,0.399-0.823,0.382c-0.321,0.017-0.63-0.126-0.825-0.382c-0.212-0.321-0.313-0.701-0.289-1.085c-0.024-0.382,0.077-0.762,0.289-1.082c0.196-0.254,0.504-0.396,0.825-0.379c0.32-0.017,0.628,0.124,0.823,0.379c0.21,0.32,0.311,0.7,0.287,1.082C9.796,77.875,9.695,78.255,9.485,78.576z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M14.509,75.036c-0.323-0.015-0.641,0.087-0.895,0.287c-0.197,0.186-0.363,0.401-0.493,0.639v-0.814h-1.162v4.667h1.23v-2.227c-0.017-0.284,0.031-0.569,0.141-0.832c0.207-0.341,0.594-0.529,0.99-0.48c0.037-0.003,0.086-0.003,0.146,0c0.06,0.003,0.129,0.008,0.206,0.017v-1.252l-0.105-0.006H14.509z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M17.956,75.045L17.956,75.045c-0.35-0.008-0.692,0.102-0.97,0.313c-0.165,0.136-0.307,0.298-0.42,0.48v-0.69h-1.17v6.506h1.21v-2.435c0.105,0.171,0.238,0.323,0.394,0.45c0.269,0.193,0.595,0.291,0.926,0.279c0.55,0.014,1.077-0.224,1.43-0.647c0.405-0.54,0.602-1.208,0.555-1.882c0.047-0.643-0.157-1.278-0.568-1.775C18.988,75.255,18.483,75.036,17.956,75.045z M18.382,78.534c-0.172,0.259-0.467,0.409-0.778,0.394c-0.219,0.005-0.433-0.063-0.609-0.193c-0.318-0.291-0.479-0.716-0.433-1.145c-0.009-0.288,0.035-0.576,0.129-0.849c0.126-0.393,0.501-0.651,0.913-0.63c0.324-0.024,0.633,0.137,0.8,0.416c0.168,0.29,0.253,0.621,0.246,0.956C18.668,77.853,18.575,78.219,18.382,78.534z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M46.708,62.444c-0.299,0.271-0.465,0.659-0.455,1.063c-0.008,0.389,0.146,0.764,0.424,1.036c0.271,0.278,0.646,0.431,1.034,0.424c0.382,0.006,0.75-0.143,1.021-0.413c0.287-0.272,0.445-0.652,0.437-1.047c0.007-0.39-0.148-0.766-0.43-1.036c-0.27-0.279-0.643-0.434-1.031-0.427C47.335,62.042,46.976,62.186,46.708,62.444z M48.561,62.663c0.227,0.222,0.353,0.527,0.35,0.845c0.005,0.322-0.122,0.633-0.352,0.859c-0.221,0.231-0.528,0.359-0.848,0.354c-0.32,0.005-0.629-0.123-0.851-0.354c-0.232-0.225-0.36-0.536-0.355-0.86c-0.004-0.317,0.122-0.622,0.348-0.845c0.222-0.237,0.533-0.369,0.857-0.364C48.033,62.295,48.342,62.427,48.561,62.663z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M47.495,63.746h0.088l0.093,0.006c0.049-0.001,0.099,0.006,0.146,0.02c0.055,0.02,0.099,0.064,0.119,0.119c0.018,0.065,0.027,0.132,0.026,0.2c0.001,0.075,0.01,0.15,0.026,0.224h0.4l-0.014-0.046c-0.005-0.015-0.009-0.031-0.011-0.047c-0.001-0.016-0.001-0.031,0-0.047v-0.144c0.012-0.136-0.041-0.27-0.142-0.362c-0.069-0.048-0.147-0.08-0.23-0.093c0.102-0.008,0.199-0.046,0.28-0.109c0.082-0.074,0.125-0.182,0.116-0.292c0.01-0.16-0.074-0.311-0.216-0.386c-0.098-0.051-0.206-0.081-0.317-0.087c-0.022,0-0.133,0-0.333,0h-0.45v1.616h0.419V63.746z M47.5,62.986h0.1c0.083-0.003,0.166,0.008,0.245,0.034c0.08,0.024,0.134,0.1,0.131,0.184c0.007,0.074-0.023,0.146-0.081,0.192c-0.069,0.043-0.15,0.064-0.231,0.059H47.5V62.986z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M34.9,48.229c1.091-1.372,1.681-3.076,1.673-4.829c0.102-1.848-0.575-3.655-1.867-4.981c-0.928-0.81-2.008-1.43-3.175-1.823c0.835-0.393,1.588-0.941,2.218-1.616c1.017-1.19,1.547-2.72,1.485-4.284c0.026-1.409-0.394-2.79-1.2-3.946c-1.367-1.929-3.667-2.893-6.899-2.893H15.276v27.605h11.675C30.584,51.461,33.233,50.384,34.9,48.229z M18.944,26.977h6.67c1.25-0.06,2.5,0.119,3.683,0.526c1.453,0.601,2.345,2.079,2.2,3.645c0.138,1.546-0.681,3.021-2.067,3.72c-1.163,0.494-2.422,0.719-3.683,0.658l-6.803,0.001V26.977z M18.944,38.549h7.423c1.285-0.046,2.566,0.152,3.777,0.583c1.715,0.608,2.814,2.284,2.688,4.099c0.031,1.088-0.292,2.156-0.921,3.044c-0.977,1.328-2.612,1.992-4.905,1.992l-8.061-0.003V38.549z\">\n\t\t\t\t</path>\n\t\t\t\t<path\n\t\t\t\t\td=\"M24.933,61.589c3.205,0.006,6.378-0.635,9.329-1.885c12.074-5.179,17.731-19.111,12.685-31.242c-5.085-12.226-19.119-18.014-31.345-12.929C6.738,19.285,0.976,27.975,0.971,37.601C0.963,50.842,11.691,61.582,24.933,61.589z M24.933,16.352V16.35c2.844-0.004,5.66,0.565,8.278,1.675c10.649,4.609,15.64,16.902,11.219,27.63c-4.477,10.863-16.913,16.04-27.776,11.562C8.79,53.889,3.678,46.18,3.673,37.64C3.665,25.891,13.183,16.36,24.933,16.352z\">\n\t\t\t\t</path>\n\t\t\t\t<rect x=\"0.288\" y=\"66.315\" width=\"48.614\" height=\"4.051\"></rect>\n\t\t\t</g>\n\t\t</svg>\n\t</div>\n\t<div class=\"flex justify-center items-center gap-md\">\n        <a href=\"https://www.facebook.com/olukai/\" target=\"_blank\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" shape-rendering=\"geometricPrecision\" text-rendering=\"geometricPrecision\" image-rendering=\"optimizeQuality\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" viewBox=\"0 0 509 509\" height=\"30px\" width=\"30px\"><g fill-rule=\"nonzero\"><path fill=\"#0866FF\" d=\"M509 254.5C509 113.94 395.06 0 254.5 0S0 113.94 0 254.5C0 373.86 82.17 474 193.02 501.51V332.27h-52.48V254.5h52.48v-33.51c0-86.63 39.2-126.78 124.24-126.78 16.13 0 43.95 3.17 55.33 6.33v70.5c-6.01-.63-16.44-.95-29.4-.95-41.73 0-57.86 15.81-57.86 56.91v27.5h83.13l-14.28 77.77h-68.85v174.87C411.35 491.92 509 384.62 509 254.5z\"/><path fill=\"#fff\" d=\"M354.18 332.27l14.28-77.77h-83.13V227c0-41.1 16.13-56.91 57.86-56.91 12.96 0 23.39.32 29.4.95v-70.5c-11.38-3.16-39.2-6.33-55.33-6.33-85.04 0-124.24 40.16-124.24 126.78v33.51h-52.48v77.77h52.48v169.24c19.69 4.88 40.28 7.49 61.48 7.49 10.44 0 20.72-.64 30.83-1.86V332.27h68.85z\"/></g></svg>\n        </a>\n        <a href=\"https://x.com/olukai\" target=\"_blank\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" shape-rendering=\"geometricPrecision\" text-rendering=\"geometricPrecision\" image-rendering=\"optimizeQuality\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" viewBox=\"0 0 512 509.64\" height=\"30px\" width=\"30px\"><rect width=\"512\" height=\"509.64\" rx=\"115.61\" ry=\"115.61\"/><path fill=\"#fff\" fill-rule=\"nonzero\" d=\"M323.74 148.35h36.12l-78.91 90.2 92.83 122.73h-72.69l-56.93-74.43-65.15 74.43h-36.14l84.4-96.47-89.05-116.46h74.53l51.46 68.04 59.53-68.04zm-12.68 191.31h20.02l-129.2-170.82H180.4l130.66 170.82z\"/></svg>\n        </a>\n        <a href=\"https://www.instagram.com/olukai/\" target=\"_blank\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" x=\"0px\" y=\"0px\" width=\"38px\" height=\"38px\" viewBox=\"0 0 48 48\">\n            <radialGradient id=\"yOrnnhliCrdS2gy~4tD8ma_Xy10Jcu1L2Su_gr1\" cx=\"19.38\" cy=\"42.035\" r=\"44.899\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#fd5\"></stop><stop offset=\".328\" stop-color=\"#ff543f\"></stop><stop offset=\".348\" stop-color=\"#fc5245\"></stop><stop offset=\".504\" stop-color=\"#e64771\"></stop><stop offset=\".643\" stop-color=\"#d53e91\"></stop><stop offset=\".761\" stop-color=\"#cc39a4\"></stop><stop offset=\".841\" stop-color=\"#c837ab\"></stop></radialGradient><path fill=\"url(#yOrnnhliCrdS2gy~4tD8ma_Xy10Jcu1L2Su_gr1)\" d=\"M34.017,41.99l-20,0.019c-4.4,0.004-8.003-3.592-8.008-7.992l-0.019-20\tc-0.004-4.4,3.592-8.003,7.992-8.008l20-0.019c4.4-0.004,8.003,3.592,8.008,7.992l0.019,20\tC42.014,38.383,38.417,41.986,34.017,41.99z\"></path><radialGradient id=\"yOrnnhliCrdS2gy~4tD8mb_Xy10Jcu1L2Su_gr2\" cx=\"11.786\" cy=\"5.54\" r=\"29.813\" gradientTransform=\"matrix(1 0 0 .6663 0 1.849)\" gradientUnits=\"userSpaceOnUse\"><stop offset=\"0\" stop-color=\"#4168c9\"></stop><stop offset=\".999\" stop-color=\"#4168c9\" stop-opacity=\"0\"></stop></radialGradient><path fill=\"url(#yOrnnhliCrdS2gy~4tD8mb_Xy10Jcu1L2Su_gr2)\" d=\"M34.017,41.99l-20,0.019c-4.4,0.004-8.003-3.592-8.008-7.992l-0.019-20\tc-0.004-4.4,3.592-8.003,7.992-8.008l20-0.019c4.4-0.004,8.003,3.592,8.008,7.992l0.019,20\tC42.014,38.383,38.417,41.986,34.017,41.99z\"></path><path fill=\"#fff\" d=\"M24,31c-3.859,0-7-3.14-7-7s3.141-7,7-7s7,3.14,7,7S27.859,31,24,31z M24,19c-2.757,0-5,2.243-5,5\ts2.243,5,5,5s5-2.243,5-5S26.757,19,24,19z\"></path><circle cx=\"31.5\" cy=\"16.5\" r=\"1.5\" fill=\"#fff\"></circle><path fill=\"#fff\" d=\"M30,37H18c-3.859,0-7-3.14-7-7V18c0-3.86,3.141-7,7-7h12c3.859,0,7,3.14,7,7v12\tC37,33.86,33.859,37,30,37z M18,13c-2.757,0-5,2.243-5,5v12c0,2.757,2.243,5,5,5h12c2.757,0,5-2.243,5-5V18c0-2.757-2.243-5-5-5H18z\"></path>\n            </svg>\n        </a>\n        <a href=\"https://www.youtube.com/@OluKai\" target=\"_blank\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 333333 333333\" shape-rendering=\"geometricPrecision\" text-rendering=\"geometricPrecision\" image-rendering=\"optimizeQuality\" fill-rule=\"evenodd\" clip-rule=\"evenodd\" height=\"40px\" width=\"40px\"><path d=\"M329930 100020s-3254-22976-13269-33065c-12691-13269-26901-13354-33397-14124-46609-3396-116614-3396-116614-3396h-122s-69973 0-116608 3396c-6522 793-20712 848-33397 14124C6501 77044 3316 100020 3316 100020S-1 126982-1 154001v25265c0 26962 3315 53979 3315 53979s3254 22976 13207 33082c12685 13269 29356 12838 36798 14254 26685 2547 113354 3315 113354 3315s70065-124 116675-3457c6522-770 20706-848 33397-14124 10021-10089 13269-33090 13269-33090s3319-26962 3319-53979v-25263c-67-26962-3384-53979-3384-53979l-18 18-2-2zM132123 209917v-93681l90046 46997-90046 46684z\" fill=\"red\"/></svg>\n        </a>\n    </div>\n\n</div>\n\n<span>© 2024 OluKai. All Rights Reserved.</span>",
          "credits_link_list": "footer-policies"
        }
      },
      "mega-menu": {
        "type": "mega-menu",
        "blocks": {
          "mens-d82fa995-f888-4e67-83dd-b4fdd4d7cbd4": {
            "type": "menu",
            "settings": {
              "title": "Mens",
              "teleport": "#mega-mens",
              "redirect": "Mens",
              "wrapper_style_background_color": "#fcf9f3",
              "container_class_container": "w-full",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "mens-2acf5995-d6c2-4aef-b867-91b91165042d": {
            "type": "frame",
            "settings": {
              "title": "⤷ Mens Menu",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "#ffffff",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:grid lg:grid-cols-6",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-left layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "",
              "article_class_horizontal_padding_desktop": "",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "mens-menu_item_kdwAPT": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "col-span-1",
              "item_class_width_desktop": "lg:col-span-1",
              "link_list": "mega-menu-mens-featured",
              "menu_item_interaction": "grouped",
              "item_class_display": "hidden",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "mens-menu_item_NrBgKw": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:col-span-3",
              "link_list": "mega-menu-mens",
              "menu_item_interaction": "grouped",
              "item_class_display": "hidden",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "grid grid-cols-1",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "frame_f4B8dy": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:hidden",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom": ""
            }
          },
          "menu_item_Jw98Xy": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:col-span-3",
              "link_list": "mega-menu-mens-mobile",
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:hidden",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "grid grid-cols-1",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "break_79cH8L": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "mens-button_LQERc7": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_text": "Shop All Men's",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "https://olukai.com/collections/men-all",
              "onclick": "",
              "form_validity": false,
              "button_class_custom_classes": "lg:!hidden mr-auto ml-4 white-text",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "mens-content_item_JpnWR8": {
            "type": "content-item",
            "settings": {
              "item_class_width": "col-span-1",
              "item_class_width_desktop": "lg:col-span-2",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-left layout-bottom",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "shopify://collections/mens-slippers-guide",
              "liquid_link": "",
              "image": "shopify://shop_images/Mens_-_Mobile_eaee5610-cb45-4333-a37e-ca0612c4abfc.jpg",
              "image_desktop": "shopify://shop_images/Mens_-_Desktop_4688fd15-68b6-453c-9917-3653da7fc2ae.jpg",
              "image_class_position": "absolute inset-0 h-full w-full object-cover",
              "image_loading": "lazy",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-left layout-bottom",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-sm",
              "content_class_horizontal_padding": "px-sm",
              "content_class_gap": "gap-md",
              "content_class_vertical_padding_desktop": "lg:py-3xl",
              "content_class_horizontal_padding_desktop": "lg:px-lg",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-[4/3]",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-left",
              "content_style_color": "#ffffff",
              "text_item_1_text": "Slippers, Perfected.",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-subline",
              "text_item_1_class_type_size": "@include TypeSize",
              "text_item_1_style_color": "#ffffff",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-col",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "Learn More",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://collections/mens-slippers-guide",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--secondary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "@include ButtonStyle",
              "button_3_class_size": ""
            }
          },
          "mens-frame_nr6R3C": {
            "type": "frame",
            "settings": {
              "title": "⤷ Footer",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "col-span-4",
              "article_class_width_desktop": "lg:col-span-6",
              "article_style_background_color": "#def1f2",
              "article_class_display": "hidden",
              "article_class_display_desktop": "lg:grid lg:grid-cols-6",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-left layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom": "mega-menu__footer"
            }
          },
          "mens-frame_9LDUxH": {
            "type": "frame",
            "settings": {
              "title": "⤷ Title Col",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "col-span-1",
              "article_class_width_desktop": "lg:col-span-1",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:grid lg:grid-cols-1",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-left layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom": "mega-menu__footer-col"
            }
          },
          "mens-title_HfY8V6": {
            "type": "title",
            "settings": {
              "title_text": "Shop by Activity",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "p",
              "title_class_type_style": "type-item",
              "title_class_type_size": "@include TypeSize",
              "title_style_color": "",
              "title_class_justification": "text-left",
              "text_class_wrap": "text-wrap"
            }
          },
          "mens-break_mWhAdX": {
            "type": "break",
            "settings": {
              "title": "⤶ Title Col End"
            }
          },
          "mens-menu_item_JiRw3w": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "col-span-4",
              "item_class_width_desktop": "lg:col-span-5",
              "link_list": "mega-menu-mens-footer",
              "menu_item_interaction": "grouped",
              "item_class_display": "grid grid-cols-1",
              "item_class_display_desktop": "lg:grid lg:grid-cols-1",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "mens-break_Kj8HzG": {
            "type": "break",
            "settings": {
              "title": "⤶ Footer End"
            }
          },
          "mens-10f68e44-88ac-412f-9e90-0e4b860b37b5": {
            "type": "break",
            "settings": {
              "title": "⤶ Mens Menu End"
            }
          },
          "womens-71e690f2-b86f-4188-8c76-d5a53b8a8ddd": {
            "type": "menu",
            "settings": {
              "title": "Womens",
              "teleport": "#mega-womens",
              "redirect": "Womens",
              "wrapper_style_background_color": "#fcf9f3",
              "container_class_container": "w-full",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "womens-frame_mbmWeJ": {
            "type": "frame",
            "settings": {
              "title": "⤷ Womens Menu",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "#ffffff",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:grid lg:grid-cols-6",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-left layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "",
              "article_class_horizontal_padding_desktop": "",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "womens-menu_item_kdwAPT": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "col-span-1",
              "item_class_width_desktop": "lg:col-span-1",
              "link_list": "mega-menu-womens-featured",
              "menu_item_interaction": "grouped",
              "item_class_display": "hidden",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": "Sandals"
            }
          },
          "womens-menu_item_NrBgKw": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:col-span-3",
              "link_list": "mega-menu-womens",
              "columns": -4,
              "menu_item_interaction": "grouped",
              "item_class_display": "hidden",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "grid grid-cols-1",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "frame_mMRR4P": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:hidden",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom": ""
            }
          },
          "menu_item_NVJVit": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:col-span-3",
              "link_list": "mega-menu-womens-mobile",
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "grid grid-cols-1",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "break_AbbpMe": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "womens-button_LQERc7": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_text": "Shop All Women's",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://collections/women-all",
              "onclick": "",
              "form_validity": false,
              "button_class_custom_classes": "lg:!hidden mr-auto ml-4",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "womens-content_item_JpnWR8": {
            "type": "content-item",
            "settings": {
              "item_class_width": "col-span-1",
              "item_class_width_desktop": "lg:col-span-2",
              "item_class_visibility": "",
              "item_class_display": "grid grid-cols-1",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-left layout-bottom",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "shopify://collections/womens-slippers-guide",
              "liquid_link": "",
              "image": "shopify://shop_images/Womens_-_Mobile_a4d6a452-7510-4b97-ac65-721ff90933fc.jpg",
              "image_desktop": "shopify://shop_images/Womens_-_Desktop_62cbb874-b492-43fb-aab2-7f6b0931a9aa.jpg",
              "image_class_position": "absolute inset-0 h-full w-full object-cover",
              "image_loading": "lazy",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "container",
              "content_class_width_desktop": "lg:container",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-left layout-bottom",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-sm",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "gap-md",
              "content_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "content_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-[4/3]",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-left",
              "content_style_color": "#ffffff",
              "text_item_1_text": "Slippers, Perfected.",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-subline",
              "text_item_1_class_type_size": "@include TypeSize",
              "text_item_1_style_color": "#ffffff",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-col",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "Learn More",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://collections/womens-slippers-guide",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--secondary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "@include ButtonStyle",
              "button_3_class_size": ""
            }
          },
          "womens-frame_nr6R3C": {
            "type": "frame",
            "settings": {
              "title": "⤷ Footer",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "col-span-4",
              "article_class_width_desktop": "lg:col-span-6",
              "article_style_background_color": "#def1f2",
              "article_class_display": "hidden",
              "article_class_display_desktop": "lg:grid lg:grid-cols-6",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-left layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom": "mega-menu__footer"
            }
          },
          "womens-frame_9LDUxH": {
            "type": "frame",
            "settings": {
              "title": "⤷ Title Col",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "col-span-1",
              "article_class_width_desktop": "lg:col-span-1",
              "article_style_background_color": "",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:grid lg:grid-cols-1",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-left layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom": "mega-menu__footer-col"
            }
          },
          "womens-title_HfY8V6": {
            "type": "title",
            "settings": {
              "title_text": "Shop by Activity",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "p",
              "title_class_type_style": "type-item",
              "title_class_type_size": "@include TypeSize",
              "title_style_color": "",
              "title_class_justification": "text-left",
              "text_class_wrap": "text-wrap"
            }
          },
          "womens-break_mWhAdX": {
            "type": "break",
            "settings": {
              "title": "⤶ Title Col End"
            }
          },
          "womens-menu_item_JiRw3w": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "col-span-4",
              "item_class_width_desktop": "lg:col-span-5",
              "link_list": "mega-menu-womens-footer",
              "menu_item_interaction": "grouped",
              "item_class_display": "grid grid-cols-1",
              "item_class_display_desktop": "lg:grid lg:grid-cols-1",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "womens-break_Kj8HzG": {
            "type": "break",
            "settings": {
              "title": "⤶ Footer End"
            }
          },
          "womens-09e48b1e-f439-402d-87e0-1e55465f13a0": {
            "type": "break",
            "settings": {
              "title": "⤶ Womens End"
            }
          },
          "menu_GLyDqj": {
            "type": "menu",
            "settings": {
              "title": "Gift Guide",
              "teleport": "#mega-gift-guide",
              "redirect": "",
              "wrapper_style_background_color": "#ffffff",
              "container_class_container": "w-full",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "frame_nrRYnJ": {
            "type": "frame",
            "settings": {
              "title": "⤷ Gift Guide Menu",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:container",
              "article_style_background_color": "#ffffff",
              "article_class_display": "grid grid-cols-1",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-0",
              "article_class_vertical_padding_desktop": "lg:py-lg",
              "article_class_horizontal_padding_desktop": "lg:px-0",
              "article_class_gap_desktop": "lg:gap-lg",
              "article_class_custom": ""
            }
          },
          "content_item_gyaNUD": {
            "type": "content-item",
            "settings": {
              "item_class_width": "container",
              "item_class_width_desktop": "lg:container",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "shopify://collections/mens-holiday-gift-guide",
              "liquid_link": "",
              "image": "shopify://shop_images/mens-gift_guide_ea7902c3-93ea-44d9-9e28-ab195ec87bc3.jpg",
              "image_class_position": "absolute inset-0 h-full w-full object-cover",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-left layout-bottom",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-md",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-sm",
              "content_class_aspect": "aspect-[1/1]",
              "content_class_aspect_desktop": "lg:aspect-[1/1]",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-left layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "#ffffff",
              "text_item_1_text": "Men's Gift Guide",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-subline",
              "text_item_1_class_type_size": "type--lg",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "Shop Now",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--secondary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "content_item_HMgaqa": {
            "type": "content-item",
            "settings": {
              "item_class_width": "container",
              "item_class_width_desktop": "lg:container",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "shopify://collections/womens-holiday-gift-guide",
              "liquid_link": "",
              "image": "shopify://shop_images/womens-gift_guide_b12ddc4b-9a2d-4dd7-baa1-1cc386e3d590.jpg",
              "image_class_position": "absolute inset-0 h-full w-full object-cover",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-left layout-bottom",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-md",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-sm",
              "content_class_aspect": "aspect-[1/1]",
              "content_class_aspect_desktop": "lg:aspect-[1/1]",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-left layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "#ffffff",
              "text_item_1_text": "Women's Gift Guide",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-subline",
              "text_item_1_class_type_size": "type--lg",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "Shop Now",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--secondary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "content_item_hJKMYP": {
            "type": "content-item",
            "settings": {
              "item_class_width": "container",
              "item_class_width_desktop": "lg:container",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-middle",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "py-0",
              "item_class_horizontal_padding": "px-0",
              "item_class_gap": "gap-0",
              "item_class_vertical_padding_desktop": "lg:py-0",
              "item_class_horizontal_padding_desktop": "lg:px-0",
              "item_class_gap_desktop": "lg:gap-0",
              "item_style_background": "",
              "content_style_background": "",
              "link": "shopify://products/olukai-e-gift-card",
              "liquid_link": "",
              "image": "shopify://shop_images/card-gift_guide_5b5d0b48-d4fc-4e31-bdfd-c0b6de1b7486.jpg",
              "image_class_position": "absolute inset-0 h-full w-full object-cover",
              "image_loading": "eager",
              "media_class_width": "w-full",
              "media_class_width_desktop": "lg:w-full",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-left layout-bottom",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-0",
              "content_class_horizontal_padding": "px-0",
              "content_class_gap": "gap-0",
              "content_class_vertical_padding_desktop": "lg:py-md",
              "content_class_horizontal_padding_desktop": "lg:px-0",
              "content_class_gap_desktop": "lg:gap-sm",
              "content_class_aspect": "aspect-[1/1]",
              "content_class_aspect_desktop": "lg:aspect-[1/1]",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-left layout-middle",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "py-0",
              "text_stack_class_horizontal_padding": "px-0",
              "text_stack_class_gap": "gap-0",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "lg:px-0",
              "text_stack_class_gap_desktop": "lg:gap-0",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-center",
              "content_style_color": "#ffffff",
              "text_item_1_text": "Digital Gift Card",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-subline",
              "text_item_1_class_type_size": "type--lg",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "",
              "text_item_2_class_type_size": "",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "",
              "text_item_3_class_type_size": "",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-col",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "py-0",
              "buttons__horizontal_padding": "px-0",
              "buttons__gap": "gap-0",
              "buttons__vertical_padding_desktop": "lg:py-0",
              "buttons__horizontal_padding_desktop": "lg:px-0",
              "buttons__gap_desktop": "lg:gap-0",
              "button_1_text": "Shop Now",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--secondary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "button--primary",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "button--primary",
              "button_3_class_size": ""
            }
          },
          "break_magRdb": {
            "type": "break",
            "settings": {
              "title": "⤶ Gift Guide End"
            }
          },
          "explore-c59a01c5-0b2b-440e-859d-480a52de5f59": {
            "type": "menu",
            "settings": {
              "title": "Explore",
              "teleport": "#mega-explore",
              "redirect": "",
              "wrapper_style_background_color": "#fcf9f3",
              "container_class_container": "w-full",
              "container_class_vertical_padding": "py-0",
              "container_class_horizontal_padding": "px-0",
              "container_class_vertical_padding_desktop": "lg:py-0",
              "container_class_horizontal_padding_desktop": "lg:px-0"
            }
          },
          "explore-frame_mbmWeJ": {
            "type": "frame",
            "settings": {
              "title": "⤷ Explore Menu",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_style_background_color": "#ffffff",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:grid lg:grid-cols-6",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-left layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-0",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "",
              "article_class_horizontal_padding_desktop": "",
              "article_class_gap_desktop": "lg:gap-0",
              "article_class_custom": ""
            }
          },
          "explore-menu_item_kdwAPT": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "col-span-1",
              "item_class_width_desktop": "lg:col-span-1",
              "link_list": "mega-menu-explore-featured",
              "menu_item_interaction": "grouped",
              "item_class_display": "hidden",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "flex",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-col",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": ""
            }
          },
          "explore-menu_item_NrBgKw": {
            "type": "menu-item",
            "settings": {
              "item_class_width": "w-full",
              "item_class_width_desktop": "lg:col-span-3",
              "link_list": "mega-menu-explore",
              "menu_item_interaction": "grouped",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "menu_class_display": "grid grid-cols-1",
              "menu_class_display_desktop": "lg:flex",
              "menu_class_direction": "flex-row",
              "menu_class_layout": "layout-top",
              "menu_class_layout_spacing": "layout-space-packed",
              "menu_class_vertical_padding": "@include Spacing prop:py",
              "menu_class_horizontal_padding": "@include Spacing prop:px",
              "menu_class_gap": "@include Spacing prop:gap",
              "menu_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "menu_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "menu_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "default_open_mobile": "Stories\nCompany"
            }
          },
          "explore-button_LQERc7": {
            "type": "button",
            "disabled": true,
            "settings": {
              "style": "button--tertiary",
              "button_text": "Shop All Men",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "",
              "onclick": "",
              "form_validity": false,
              "button_class_custom_classes": "lg:!hidden mr-auto ml-4",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "explore-content_item_JpnWR8": {
            "type": "content-item",
            "settings": {
              "item_class_width": "col-span-1",
              "item_class_width_desktop": "lg:col-span-2",
              "item_class_visibility": "",
              "item_class_display": "grid grid-cols-1",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-left layout-bottom",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "shopify://blogs/news/shark-dive-mike-coots",
              "liquid_link": "",
              "image": "shopify://shop_images/hp-mobile-aa_mike_coots_1.jpg",
              "image_class_position": "absolute inset-0 h-full w-full object-cover",
              "image_loading": "lazy",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "container",
              "content_class_width_desktop": "lg:container",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-left layout-bottom",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "py-sm",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "gap-md",
              "content_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "content_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-[4/3]",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-full",
              "text_stack_class_width_desktop": "lg:w-full",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-row",
              "text_stack_class_layout": "layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "@include Spacing prop:gap",
              "text_stack_class_vertical_padding_desktop": "lg:py-0",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "",
              "title_image_class_width": "container",
              "title_image_class_width_desktop": "lg:container",
              "text_stack_class": "text-left",
              "content_style_color": "#ffffff",
              "text_item_1_text": "Shark Dive With Mike Coots",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-subline",
              "text_item_1_class_type_size": "@include TypeSize",
              "text_item_1_style_color": "#ffffff",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-col",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "Learn More",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "shopify://blogs/news/shark-dive-mike-coots",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "button--secondary",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "@include ButtonStyle",
              "button_3_class_size": ""
            }
          }
        },
        "block_order": [
          "mens-d82fa995-f888-4e67-83dd-b4fdd4d7cbd4",
          "mens-2acf5995-d6c2-4aef-b867-91b91165042d",
          "mens-menu_item_kdwAPT",
          "mens-menu_item_NrBgKw",
          "frame_f4B8dy",
          "menu_item_Jw98Xy",
          "break_79cH8L",
          "mens-button_LQERc7",
          "mens-content_item_JpnWR8",
          "mens-frame_nr6R3C",
          "mens-frame_9LDUxH",
          "mens-title_HfY8V6",
          "mens-break_mWhAdX",
          "mens-menu_item_JiRw3w",
          "mens-break_Kj8HzG",
          "mens-10f68e44-88ac-412f-9e90-0e4b860b37b5",
          "womens-71e690f2-b86f-4188-8c76-d5a53b8a8ddd",
          "womens-frame_mbmWeJ",
          "womens-menu_item_kdwAPT",
          "womens-menu_item_NrBgKw",
          "frame_mMRR4P",
          "menu_item_NVJVit",
          "break_AbbpMe",
          "womens-button_LQERc7",
          "womens-content_item_JpnWR8",
          "womens-frame_nr6R3C",
          "womens-frame_9LDUxH",
          "womens-title_HfY8V6",
          "womens-break_mWhAdX",
          "womens-menu_item_JiRw3w",
          "womens-break_Kj8HzG",
          "womens-09e48b1e-f439-402d-87e0-1e55465f13a0",
          "menu_GLyDqj",
          "frame_nrRYnJ",
          "content_item_gyaNUD",
          "content_item_HMgaqa",
          "content_item_hJKMYP",
          "break_magRdb",
          "explore-c59a01c5-0b2b-440e-859d-480a52de5f59",
          "explore-frame_mbmWeJ",
          "explore-menu_item_kdwAPT",
          "explore-menu_item_NrBgKw",
          "explore-button_LQERc7",
          "explore-content_item_JpnWR8"
        ],
        "custom_css": [
          ".menu-item:first-of-type {border-right: 2px solid #e6e6e6;}"
        ],
        "settings": {
          "menu_text": "Sale",
          "color_selector": "#000000"
        }
      },
      "slider-cart": {
        "type": "slider-cart",
        "blocks": {
          "frame_8Y9dfr": {
            "type": "frame",
            "settings": {
              "title": "⤷ Cart Header",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "#ffffff",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-right layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-md",
              "article_class_horizontal_padding": "px-0",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "sticky top-0 left-0 z-10 border-b"
            }
          },
          "frame_km84NC": {
            "type": "frame",
            "settings": {
              "title": "⤷ Text Container",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "rgba(0,0,0,0)",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "relative"
            }
          },
          "title_b3gB4H": {
            "type": "title",
            "settings": {
              "title_text": "",
              "title_liquid": "",
              "title_attr_x_text": "$store.cart.item_count > 0 ? 'Your Cart': 'Your Cart Is Empty'",
              "title_element": "h2",
              "title_class_type_style": "type-subline",
              "title_class_type_size": "@include TypeSize",
              "title_style_color": "#021327"
            }
          },
          "frame_UVUAcK": {
            "type": "frame",
            "settings": {
              "title": "⤷ Button Container",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-auto",
              "article_class_width_desktop": "lg:w-auto",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-right layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "absolute right-0"
            }
          },
          "button_hTWGcX": {
            "type": "button",
            "settings": {
              "style": "button--icon",
              "button_class_size": "",
              "button_text": "",
              "leading_icon": "",
              "trailing_icon": "x",
              "link": "",
              "onclick": "Modal.close()",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "break_FH9Vkb": {
            "type": "break",
            "settings": {
              "title": "⤶ Button Container"
            }
          },
          "break_YA3mRz": {
            "type": "break",
            "settings": {
              "title": "⤶ Text Container"
            }
          },
          "frame_mknhyD": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "offer_progress_EP9gbN": {
            "type": "offer_progress",
            "settings": {
              "title": "Offer Progress",
              "key": "freeshipsolo",
              "combinable": true,
              "combinable_empty_cart": false,
              "offer_style_background_color": "#ffffff",
              "offer_style_background": "",
              "offer_progress_style_background_color": "#f6f6f6",
              "offer_progress_bar_style_background_color": "#0066d2",
              "offer_threshold_passed_style_color": "#ffffff",
              "offer_threshold_label_style_color": "#00213b",
              "offer_class_display": "flex",
              "offer_class_display_desktop": "lg:flex",
              "offer_class_direction": "flex-col",
              "offer_class_layout": "layout-center layout-top",
              "offer_class_layout_spacing": "layout-space-packed",
              "offer_class_vertical_padding": "py-md",
              "offer_class_horizontal_padding": "px-xl",
              "offer_class_gap": "gap-xl",
              "offer_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "offer_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "offer_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "offer_message_class_type_style": "type-subline",
              "offer_message_class_type_size": "type--sm",
              "offer_message_style_color": "#000000"
            }
          },
          "offer_progress_RGhxad": {
            "type": "offer_progress",
            "disabled": true,
            "settings": {
              "title": "Offer Progress",
              "key": "freeshipsolo",
              "combinable": false,
              "combinable_empty_cart": false,
              "offer_style_background_color": "",
              "offer_style_background": "",
              "offer_progress_style_background_color": "#eeece1",
              "offer_progress_bar_style_background_color": "#0066d2",
              "offer_threshold_passed_style_color": "#ffffff",
              "offer_threshold_label_style_color": "#000000",
              "offer_class_display": "flex",
              "offer_class_display_desktop": "lg:flex",
              "offer_class_direction": "flex-col",
              "offer_class_layout": "layout-center layout-top",
              "offer_class_layout_spacing": "layout-space-packed",
              "offer_class_vertical_padding": "@include Spacing prop:py",
              "offer_class_horizontal_padding": "@include Spacing prop:px",
              "offer_class_gap": "gap-sm",
              "offer_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "offer_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "offer_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "offer_message_class_type_style": "@include TypeStyle",
              "offer_message_class_type_size": "@include TypeSize",
              "offer_message_style_color": ""
            }
          },
          "break_NnkGyL": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "break_FmTEdF": {
            "type": "break",
            "settings": {
              "title": "⤶ Cart Header"
            }
          },
          "frame_jUa8Xh": {
            "type": "frame",
            "settings": {
              "title": "⤷ Empty State",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.items.length == 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "#f6f6f6",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-xl",
              "article_class_horizontal_padding": "px-md",
              "article_class_gap": "gap-xl",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "title_9BWN8H": {
            "type": "title",
            "settings": {
              "title_text": "Navigate Our Collections",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "h4",
              "title_class_type_style": "type-item",
              "title_class_type_size": "@include TypeSize",
              "title_style_color": "#021327"
            }
          },
          "frame_XkcdH4": {
            "type": "frame",
            "settings": {
              "title": "⤷ Mens Buttons",
              "article_class_visibility": "",
              "inclusion_liquid": "",
              "inclusion_js": "!JSON.parse(localStorage.getItem('customer') || '{}')?.segmentation?.storeGender || JSON.parse(localStorage.getItem('customer') || '{}')?.segmentation?.storeGender === 'man'",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "gap-sm",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "button_fPcKqF": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Shop Mens",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/collections/men-all",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_TpAJBn": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Shop Womens",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/collections/women-all",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_iK98rc": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Find Your Style Quiz",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/collections/shoe-finder",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_j3jmpP": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Shop Best Sellers",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://collections/best-sellers",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "break_t36VtN": {
            "type": "break",
            "settings": {
              "title": "⤶ Mens Buttons"
            }
          },
          "frame_ne3cjQ": {
            "type": "frame",
            "settings": {
              "title": "⤷ Womens Buttons",
              "article_class_visibility": "",
              "inclusion_liquid": "",
              "inclusion_js": "JSON.parse(localStorage.getItem('customer') || '{}')?.segmentation?.storeGender === 'woman'",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "gap-sm",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "button_pFhqBU": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Shop Womens",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/collections/women-all",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_3PVK8Q": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Shop Mens",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/collections/men-all",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_Eqj7Qf": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Find Your Style Quiz",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/collections/shoe-finder",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "button_ptQezj": {
            "type": "button",
            "settings": {
              "style": "button--tertiary",
              "button_class_size": "button button--large",
              "button_text": "Shop Best Sellers",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "shopify://collections/best-sellers",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "break_jAxyw6": {
            "type": "break",
            "settings": {
              "title": "⤶ Womens Buttons"
            }
          },
          "frame_znRA7w": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "container",
              "article_class_width_desktop": "lg:container",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "px-xs",
              "article_class_gap": "gap-md",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "content_item_DWMFNE": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-1/3",
              "item_class_width_desktop": "lg:w-1/3",
              "item_class_aspect": "aspect-auto",
              "item_class_aspect_desktop": "lg:aspect-auto",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-auto",
              "media_class_width_desktop": "lg:w-auto",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-auto",
              "content_class_width_desktop": "lg:w-auto",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-center layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "content_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-col",
              "text_stack_class_layout": "layout-center layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "gap-2xs",
              "text_stack_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "<svg width=\"36\" height=\"36\" viewBox=\"0 0 36 36\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_410_487)\">\n<mask id=\"mask0_410_487\" style=\"mask-type:luminance\" maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"36\" height=\"36\">\n<path d=\"M36 0H0V36H36V0Z\" fill=\"white\"/>\n</mask>\n<g mask=\"url(#mask0_410_487)\">\n<mask id=\"mask1_410_487\" style=\"mask-type:luminance\" maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"36\" height=\"36\">\n<path d=\"M36 36H0V0H36V36ZM17.925 26.6167C18.3058 26.6137 18.6817 26.7022 19.0211 26.8749C19.3605 27.0476 19.6533 27.2994 19.875 27.609L19.9042 27.636C19.9927 27.721 20.1108 27.7684 20.2335 27.768C20.3134 27.7669 20.3919 27.7462 20.4619 27.7078C20.532 27.6693 20.5916 27.6143 20.6355 27.5475C20.7191 27.1441 20.9048 26.769 21.1749 26.4579C21.445 26.1468 21.7903 25.9102 22.1779 25.7708C22.5656 25.6313 22.9825 25.5937 23.3888 25.6614C23.7952 25.7292 24.1773 25.9001 24.4988 26.1577C24.5142 26.1587 24.5298 26.1587 24.5452 26.1577C24.6606 26.1578 24.772 26.116 24.8587 26.04C24.9499 25.9622 25.0076 25.8524 25.02 25.7332C24.9974 25.3574 25.0633 24.9815 25.2124 24.6357C25.3615 24.29 25.5897 23.9841 25.8786 23.7426C26.1675 23.5011 26.5091 23.3308 26.8758 23.2454C27.2425 23.16 27.6241 23.1618 27.99 23.2507H27.9967H28.0252C28.104 23.2506 28.1815 23.231 28.251 23.1938C28.3204 23.1566 28.3795 23.1028 28.4232 23.0372C28.4669 22.9717 28.4937 22.8964 28.5014 22.818C28.509 22.7396 28.4972 22.6605 28.467 22.5877L28.3237 22.2472L28.3988 22.1415C28.3314 21.8029 28.3378 21.4538 28.4176 21.118C28.4974 20.7822 28.6488 20.4675 28.8613 20.1955C29.0738 19.9235 29.3425 19.7005 29.649 19.5418C29.9555 19.383 30.2927 19.2923 30.6375 19.2757C30.7168 19.1984 30.7667 19.0958 30.7786 18.9856C30.7905 18.8754 30.7637 18.7645 30.7028 18.672C30.666 18.6345 30.6367 18.6015 30.609 18.5677L30.2873 18.2955L30.3247 18.1493C30.0763 17.6845 29.9877 17.1509 30.0727 16.6308C30.1577 16.1107 30.4115 15.633 30.795 15.2715L30.81 15.069L31.245 14.9295C31.2595 14.925 31.2738 14.9198 31.2878 14.9137L31.344 14.8845L31.3567 14.8778C31.4133 14.8417 31.4617 14.7941 31.4985 14.738C31.5354 14.682 31.56 14.6188 31.5707 14.5525C31.5814 14.4863 31.5779 14.4185 31.5606 14.3537C31.5432 14.2889 31.5124 14.2285 31.47 14.1765C31.3745 14.1269 31.2828 14.0705 31.1955 14.0077L30.795 13.8907L30.7703 13.6238C30.4847 13.2945 30.2942 12.8938 30.2191 12.4645C30.1441 12.0352 30.1873 11.5936 30.3442 11.187L30.2588 10.8585L30.6427 10.5893C30.717 10.5369 30.7749 10.4646 30.8098 10.3807C30.8447 10.2969 30.8552 10.2048 30.84 10.1153C30.8206 10.0053 30.763 9.90564 30.6774 9.8339C30.5918 9.76216 30.4837 9.72289 30.372 9.723H30.3375L29.8688 9.75675L29.7495 9.49425C29.2909 9.28706 28.9091 8.94097 28.6579 8.50491C28.4068 8.06884 28.2991 7.56488 28.35 7.06425L28.2142 6.87375L28.4325 6.49875C28.4887 6.4028 28.5093 6.29018 28.491 6.18054C28.4726 6.07089 28.4164 5.97117 28.332 5.89875C28.2461 5.82281 28.1354 5.78093 28.0208 5.781C27.9867 5.78112 27.9528 5.78464 27.9195 5.7915C27.8999 5.7952 27.8805 5.80047 27.8617 5.80725L27.4552 5.94975L27.3518 5.84475C26.7945 5.8296 26.2599 5.62151 25.839 5.256C25.4287 4.90273 25.1516 4.41974 25.0537 3.88725L24.9592 3.825L25.0125 3.41925C25.0125 3.41925 25.0125 3.4125 25.0125 3.40875C25.0128 3.40476 25.0128 3.40074 25.0125 3.39675C25.0125 3.381 25.0125 3.3645 25.0125 3.34725C25.0106 3.26006 24.9849 3.17505 24.9381 3.10146C24.8913 3.02788 24.8252 2.96853 24.7471 2.92987C24.6689 2.89121 24.5816 2.87472 24.4947 2.88219C24.4078 2.88966 24.3247 2.92081 24.2542 2.97225L23.925 3.216L23.8703 3.189C23.5563 3.33353 23.2146 3.408 22.869 3.40725C22.5876 3.40725 22.3083 3.35775 22.044 3.261C21.658 3.11885 21.3146 2.88073 21.0461 2.56908C20.7776 2.25743 20.5929 1.88254 20.5095 1.47975C20.4268 1.41743 20.3258 1.38421 20.2222 1.38525C20.1539 1.38534 20.0863 1.39981 20.0239 1.42772C19.9615 1.45563 19.9056 1.49636 19.86 1.54725C19.6369 1.85146 19.3457 2.09924 19.0097 2.27076C18.6737 2.44228 18.3022 2.53277 17.925 2.535C17.2876 2.53329 16.6767 2.28022 16.2248 1.83075H16.2165L16.1632 1.7655C16.1034 1.70134 16.0471 1.63399 15.9945 1.56375L15.9877 1.5555C15.9091 1.46214 15.798 1.40204 15.6768 1.38729C15.5556 1.37254 15.4333 1.40423 15.3345 1.476C15.2517 1.87966 15.0673 2.25554 14.7988 2.56813C14.5303 2.88072 14.1865 3.1197 13.8 3.2625C13.5356 3.35904 13.2564 3.40853 12.975 3.40875C12.47 3.40716 11.9782 3.24801 11.568 2.9535C11.4995 2.90937 11.4209 2.88342 11.3397 2.87809C11.2584 2.87277 11.1771 2.88826 11.1034 2.92309C11.0298 2.95792 10.9663 3.01094 10.9188 3.07715C10.8714 3.14336 10.8416 3.22058 10.8322 3.3015C10.852 3.62622 10.8056 3.95156 10.6959 4.25783C10.5862 4.5641 10.4156 4.84493 10.1942 5.08332C9.97285 5.32172 9.70543 5.51271 9.40811 5.64475C9.1108 5.77679 8.78979 5.84713 8.4645 5.8515L8.3895 5.9355L7.992 5.81175C7.935 5.79975 7.896 5.7915 7.86075 5.7825H7.82475C7.7425 5.78265 7.66168 5.804 7.59008 5.84449C7.51849 5.88498 7.45854 5.94324 7.41602 6.01365C7.3735 6.08406 7.34985 6.16424 7.34735 6.24646C7.34486 6.32867 7.36359 6.41014 7.40175 6.483L7.59675 6.849L7.488 7.0035C7.53661 7.48964 7.43616 7.97906 7.2 8.40675C6.94932 8.86126 6.55687 9.22131 6.0825 9.432L5.99175 9.627L5.55375 9.621H5.55C5.43632 9.61931 5.32593 9.65913 5.2395 9.733C5.15308 9.80686 5.09655 9.9097 5.0805 10.0223C5.06132 10.1395 5.08819 10.2596 5.1555 10.3575C5.19 10.3935 5.22225 10.4325 5.2515 10.464L5.589 10.743L5.54475 10.9005C5.78493 11.3568 5.87047 11.8787 5.7885 12.3878C5.69898 12.9312 5.42706 13.428 5.0175 13.7963L5.00325 13.9613L4.59375 14.106L4.56225 14.118C4.54125 14.13 4.52025 14.1405 4.49925 14.151C4.44192 14.1868 4.39288 14.2345 4.35542 14.2907C4.31795 14.347 4.29292 14.4106 4.28199 14.4774C4.27106 14.5441 4.27448 14.6124 4.29203 14.6777C4.30958 14.743 4.34085 14.8038 4.38375 14.856C4.4816 14.907 4.57583 14.9646 4.66575 15.0285L4.9965 15.1478L5.01 15.3292C5.34286 15.6804 5.56179 16.124 5.63808 16.6018C5.71437 17.0797 5.64445 17.5694 5.4375 18.0068L5.48625 18.2017L5.172 18.4733C5.10028 18.5349 5.0486 18.6165 5.02358 18.7077C4.99855 18.7989 5.00132 18.8954 5.03154 18.985C5.06175 19.0746 5.11803 19.1532 5.19317 19.2106C5.2683 19.268 5.35886 19.3016 5.45325 19.3073L5.868 19.332L5.92575 19.464C6.47127 19.6611 6.92694 20.0491 7.20849 20.5562C7.49004 21.0633 7.57841 21.6553 7.45725 22.2225L7.49925 22.2833L7.3785 22.6132C7.34621 22.7043 7.34181 22.8028 7.36586 22.8964C7.38991 22.9899 7.44131 23.0741 7.5135 23.1383C7.59993 23.2132 7.71035 23.2547 7.82475 23.2552C7.84684 23.255 7.86889 23.2532 7.89075 23.25H7.91175C8.27182 23.1699 8.64551 23.174 9.00377 23.2618C9.36204 23.3495 9.69526 23.5187 9.97754 23.7562C10.2598 23.9936 10.4836 24.2929 10.6314 24.6309C10.7792 24.9688 10.8472 25.3363 10.83 25.7048V25.7325C10.8413 25.8537 10.8991 25.9658 10.9913 26.0452C11.0582 26.1038 11.1402 26.1423 11.228 26.1565C11.3158 26.1707 11.4058 26.16 11.4878 26.1255L11.775 26.0062C12.1108 25.8389 12.4806 25.7514 12.8558 25.7505C13.367 25.7506 13.8649 25.9135 14.2774 26.2156C14.6899 26.5176 14.9955 26.9432 15.15 27.4305L15.1868 27.5055C15.2255 27.5857 15.2863 27.6532 15.362 27.7001C15.4377 27.7469 15.5252 27.7713 15.6143 27.7703C15.7362 27.7701 15.8536 27.7237 15.9427 27.6405L15.9735 27.612C16.1974 27.3041 16.4908 27.0534 16.83 26.8805C17.1691 26.7075 17.5443 26.6171 17.925 26.6167Z\" fill=\"white\"/>\n</mask>\n<g mask=\"url(#mask1_410_487)\">\n<path d=\"M15.0132 20.5677L9.86934 29.4772L11.6469 29.1239L12.7942 28.8977L13.1733 30.0051L13.7567 31.7215L18.9006 22.8121L15.0132 20.5677ZM14.5237 18.741L20.7279 22.323L13.4436 34.9399L11.9071 30.4372L7.23937 31.3579L14.5237 18.741Z\" fill=\"#021327\"/>\n<path d=\"M16.7057 22.8131L21.8481 31.7199L22.4326 30.0054L22.8104 28.8988L23.959 29.1242L25.7354 29.4756L20.593 20.5687L16.7057 22.8131ZM14.879 22.3236L21.0825 18.742L28.3665 31.3582L23.6988 30.4375L22.1623 34.9402L14.879 22.3236Z\" fill=\"#021327\"/>\n</g>\n<path d=\"M15.5325 26.6632C15.8184 26.3605 16.1629 26.119 16.5451 25.9536C16.9273 25.7882 17.3391 25.7022 17.7555 25.701C18.164 25.6992 18.5686 25.7802 18.945 25.939C19.3214 26.0978 19.6617 26.3311 19.9455 26.625C20.1038 26.2365 20.3404 25.8847 20.6406 25.5917C20.9408 25.2986 21.2981 25.0704 21.6903 24.9215C22.0825 24.7725 22.5011 24.7059 22.9202 24.7259C23.3392 24.7458 23.7497 24.8518 24.126 25.0372C24.1675 24.6374 24.2876 24.2498 24.4794 23.8966C24.6713 23.5433 24.931 23.2315 25.2437 22.979C25.5565 22.7265 25.916 22.5383 26.3017 22.4252C26.6874 22.3121 27.0917 22.2763 27.4913 22.32C27.4802 22.2455 27.4754 22.1703 27.477 22.095C27.3241 21.3483 27.4564 20.5713 27.8479 19.9173C28.2393 19.2633 28.8616 18.7797 29.592 18.5617C29.578 18.5372 29.5648 18.5122 29.5523 18.4867C29.1673 17.8723 29.014 17.1407 29.12 16.4234C29.226 15.7061 29.5843 15.0501 30.1305 14.5732C30.1995 14.4917 30.2781 14.4186 30.3645 14.3557C30.2161 14.2528 30.0901 14.1209 29.994 13.968C29.6333 13.5603 29.3883 13.0634 29.2845 12.529C29.1807 11.9946 29.2219 11.4421 29.4038 10.929C29.425 10.6902 29.51 10.4615 29.6498 10.2667C29.4652 10.2164 29.2936 10.1272 29.1465 10.005C28.5784 9.73509 28.1076 9.29605 27.7988 8.74809C27.49 8.20012 27.3582 7.57005 27.4215 6.94425C27.4013 6.78422 27.4102 6.62186 27.4478 6.465H27.3833C27.3513 6.465 27.3193 6.465 27.2873 6.465C27.2453 6.465 27.2033 6.465 27.1613 6.465C26.4263 6.46433 25.7165 6.19795 25.1625 5.715C24.5733 5.20749 24.203 4.49189 24.129 3.71775C24.126 3.70225 24.1235 3.687 24.1215 3.672C23.7415 3.87937 23.3217 4.00336 22.89 4.03569C22.4583 4.06802 22.0247 4.00795 21.618 3.8595C20.8649 3.58279 20.2502 3.02194 19.9058 2.29725C19.3329 2.862 18.5607 3.17834 17.7563 3.17775C16.9526 3.17731 16.1815 2.86045 15.6098 2.29575C15.2656 3.02144 14.6506 3.58317 13.8968 3.86025C13.562 3.98247 13.2084 4.04492 12.852 4.04475C12.3414 4.04298 11.8393 3.9141 11.391 3.66975C11.3269 4.43174 10.979 5.14188 10.4162 5.65956C9.85338 6.17723 9.1167 6.46468 8.35201 6.465C8.25151 6.465 8.15026 6.45975 8.05201 6.45C8.07838 6.57136 8.08772 6.69581 8.07976 6.81975C8.15523 7.47042 8.01918 8.1281 7.69175 8.69543C7.36432 9.26276 6.86291 9.70958 6.26176 9.96975C6.14657 10.0471 6.02006 10.106 5.88676 10.1445C5.92329 10.2 5.95563 10.2582 5.98351 10.3185C6.35916 10.9339 6.50253 11.6632 6.38776 12.375C6.2678 13.124 5.87446 13.8019 5.28376 14.2777C5.23297 14.3296 5.17804 14.3773 5.11951 14.4202C5.17279 14.4609 5.22293 14.5056 5.26951 14.5537C5.77707 15.0091 6.11722 15.6215 6.23551 16.293C6.34872 16.9305 6.25516 17.5875 5.96851 18.168C5.93099 18.2976 5.8741 18.4207 5.79976 18.5332C5.8691 18.5574 5.93632 18.5873 6.00076 18.6225C6.73531 18.8609 7.3519 19.3693 7.72575 20.0451C8.09961 20.7208 8.20282 21.5133 8.01451 22.2622C8.01451 22.2832 8.01251 22.3045 8.00851 22.326C8.41793 22.28 8.83242 22.3174 9.22697 22.4361C9.62151 22.5547 9.98791 22.7521 10.304 23.0163C10.6202 23.2805 10.8795 23.606 11.0663 23.9732C11.2531 24.3404 11.3636 24.7417 11.391 25.1527C11.7598 24.971 12.1616 24.8658 12.5722 24.8436C12.9827 24.8215 13.3935 24.8827 13.7798 25.0237C14.168 25.1635 14.5235 25.3811 14.8247 25.6631C15.1258 25.9452 15.3662 26.2858 15.531 26.664M20.0393 28.1797C19.7465 28.1803 19.4647 28.068 19.2525 27.8662H19.2308H19.224C19.0724 27.612 18.8572 27.4018 18.5996 27.2562C18.3419 27.1106 18.0507 27.0347 17.7548 27.036C17.4583 27.0369 17.1671 27.1142 16.9092 27.2604C16.6513 27.4066 16.4355 27.6168 16.2825 27.8707H16.2765H16.2518C16.1218 27.9924 15.9652 28.082 15.7945 28.1325C15.6238 28.183 15.4437 28.193 15.2685 28.1616C15.0932 28.1302 14.9278 28.0583 14.7852 27.9517C14.6426 27.8451 14.5269 27.7067 14.4473 27.5475L14.4188 27.54H14.4135C14.3327 27.155 14.1219 26.8094 13.8166 26.5613C13.5113 26.3132 13.1299 26.1776 12.7365 26.1772C12.3616 26.1783 11.9972 26.3015 11.6985 26.5282L11.655 26.5065L11.6393 26.4982C11.4745 26.567 11.296 26.5962 11.1179 26.5834C10.9399 26.5706 10.7673 26.5162 10.6141 26.4246C10.4609 26.333 10.3313 26.2067 10.2358 26.0559C10.1403 25.905 10.0816 25.7339 10.0643 25.5562L10.056 25.551L10.05 25.5472C10.0812 25.2711 10.0447 24.9914 9.94362 24.7326C9.84253 24.4737 9.67991 24.2433 9.46982 24.0613C9.25972 23.8794 9.00848 23.7513 8.7378 23.6882C8.46712 23.6251 8.18515 23.6289 7.91626 23.6992C7.75366 23.7225 7.58796 23.7102 7.43054 23.6633C7.27311 23.6165 7.12769 23.5361 7.00426 23.4277C6.83052 23.2755 6.70679 23.0743 6.64925 22.8506C6.59171 22.6268 6.60304 22.3909 6.68176 22.1737L6.65551 22.1362C6.74576 21.9027 6.78356 21.6521 6.76622 21.4023C6.74889 21.1524 6.67685 20.9095 6.5552 20.6906C6.43356 20.4717 6.26528 20.2822 6.06228 20.1356C5.85929 19.9889 5.62654 19.8887 5.38051 19.842L5.36101 19.7977C5.13475 19.7841 4.91771 19.7033 4.73764 19.5656C4.55758 19.4279 4.42268 19.2396 4.35021 19.0248C4.27773 18.8101 4.27097 18.5786 4.33079 18.3599C4.39061 18.1413 4.51429 17.9455 4.68601 17.7975L4.67476 17.7532C4.90755 17.3901 4.99385 16.9521 4.91626 16.5277C4.84658 16.1233 4.63358 15.7575 4.31626 15.4972V15.4905L4.30051 15.4852C4.19107 15.3989 4.07173 15.3259 3.94501 15.2677C3.81114 15.1477 3.70758 14.9977 3.6428 14.83C3.57801 14.6623 3.55385 14.4816 3.57229 14.3027C3.59072 14.1239 3.65123 13.9519 3.74886 13.8009C3.84649 13.6499 3.97847 13.5242 4.13401 13.434C4.16101 13.4205 4.18876 13.4062 4.21501 13.3912C4.25142 13.3738 4.28872 13.3583 4.32676 13.3447V13.3237C4.52253 13.1919 4.68917 13.0214 4.81637 12.8226C4.94356 12.6238 5.02862 12.401 5.06626 12.168C5.10214 11.9474 5.09411 11.7218 5.04263 11.5043C4.99115 11.2868 4.89723 11.0816 4.76626 10.9005V10.8945V10.8825C4.74526 10.8652 4.72576 10.8472 4.70776 10.8307C4.66903 10.7823 4.62772 10.736 4.58401 10.692C4.4557 10.523 4.37712 10.3215 4.35712 10.1102C4.33712 9.89889 4.3765 9.68622 4.47082 9.49611C4.56513 9.30599 4.71065 9.14598 4.89097 9.03408C5.0713 8.92219 5.27929 8.86285 5.49151 8.86275H5.50876L5.52901 8.81925V8.814C5.95024 8.69153 6.30832 8.41193 6.52927 8.03297C6.75023 7.65401 6.81717 7.20465 6.71626 6.77775V6.77025L6.74101 6.73575C6.64898 6.56159 6.60353 6.3666 6.60905 6.1697C6.61457 5.9728 6.67089 5.78067 6.77253 5.61194C6.87417 5.44321 7.0177 5.30362 7.18918 5.20669C7.36066 5.10977 7.55428 5.05881 7.75126 5.05875C7.80369 5.05867 7.85606 5.06218 7.90801 5.06925C7.96201 5.0835 8.01826 5.0955 8.07451 5.1045L8.09476 5.1105C8.17944 5.12297 8.26492 5.12924 8.35051 5.12925C8.58787 5.12926 8.82265 5.08 9.03998 4.98456C9.25731 4.88912 9.45246 4.7496 9.61306 4.57482C9.77367 4.40005 9.89623 4.19383 9.973 3.96922C10.0498 3.74461 10.079 3.50651 10.059 3.27C10.0685 3.06739 10.1317 2.87094 10.2422 2.70086C10.3527 2.53078 10.5065 2.39319 10.6878 2.30224C10.8691 2.21128 11.0714 2.17025 11.2738 2.18334C11.4762 2.19644 11.6714 2.26319 11.8395 2.37675C12.1338 2.59082 12.4881 2.70656 12.852 2.7075C13.0521 2.70736 13.2506 2.67207 13.4385 2.60325C13.7428 2.4913 14.0095 2.29584 14.2078 2.03929C14.4061 1.78273 14.5281 1.47547 14.5598 1.15275C14.664 1.01561 14.7978 0.903784 14.9512 0.8256C15.1047 0.747417 15.2738 0.7049 15.446 0.701223C15.6182 0.697545 15.789 0.732802 15.9457 0.804363C16.1023 0.875923 16.2408 0.981939 16.3508 1.1145C16.5092 1.33945 16.7194 1.52303 16.9636 1.64977C17.2078 1.77651 17.4789 1.84269 17.754 1.84275C18.0285 1.84284 18.299 1.77717 18.5429 1.65125C18.7867 1.52533 18.9969 1.34282 19.1558 1.119C19.2635 0.988744 19.3987 0.883895 19.5517 0.81194C19.7047 0.739986 19.8717 0.702701 20.0408 0.702748C20.2183 0.702014 20.3935 0.74296 20.5523 0.822292C20.711 0.901623 20.849 1.01712 20.955 1.1595C20.988 1.4805 21.1103 1.78582 21.3081 2.04077C21.5059 2.29572 21.7713 2.49007 22.074 2.60175C22.3304 2.69546 22.6054 2.72638 22.8762 2.69193C23.147 2.65748 23.4055 2.55866 23.6303 2.40375H23.634C23.8043 2.2779 24.0062 2.20193 24.2172 2.18433C24.4282 2.16674 24.64 2.20821 24.8287 2.30411C25.0175 2.40001 25.1758 2.54656 25.2861 2.72734C25.3963 2.90813 25.454 3.11602 25.4528 3.32775C25.4528 3.34975 25.4528 3.372 25.4528 3.3945C25.4528 3.4215 25.4483 3.4485 25.4453 3.47475H25.4505H25.4543C25.4692 3.91748 25.6553 4.33714 25.9734 4.64545C26.2915 4.95375 26.7168 5.12664 27.1598 5.12775C27.2295 5.1277 27.2991 5.12345 27.3683 5.115L27.3743 5.121L27.3818 5.1285C27.602 5.05087 27.8407 5.04268 28.0657 5.10504C28.2907 5.1674 28.4912 5.29731 28.64 5.47721C28.7888 5.65712 28.8789 5.87836 28.898 6.11107C28.9171 6.34378 28.8643 6.57674 28.7468 6.7785L28.7783 6.8235L28.7918 6.843C28.6864 7.26904 28.749 7.71927 28.9665 8.10043C29.1841 8.48159 29.5399 8.76448 29.9603 8.8905L29.973 8.9175L30 8.9775C30.0285 8.9775 30.057 8.9775 30.0825 8.9775C30.3251 8.97715 30.5615 9.05426 30.7573 9.19761C30.953 9.34096 31.0979 9.54305 31.1708 9.77445C31.2437 10.0059 31.2408 10.2545 31.1625 10.4841C31.0843 10.7138 30.9348 10.9125 30.7358 11.0512L30.7485 11.1007L30.7628 11.157C30.5863 11.4914 30.5258 11.8749 30.5908 12.2474C30.6558 12.6198 30.8427 12.9602 31.122 13.215V13.2202L31.1258 13.2622C31.155 13.2712 31.1835 13.281 31.2105 13.2907C31.3202 13.3777 31.4401 13.451 31.5675 13.509C31.7008 13.6289 31.8039 13.7786 31.8684 13.9458C31.933 14.1131 31.9572 14.2932 31.939 14.4716C31.9209 14.6499 31.861 14.8215 31.7641 14.9723C31.6672 15.1232 31.5362 15.2491 31.3815 15.3397C31.3508 15.3547 31.3208 15.3705 31.2915 15.3877C31.246 15.4093 31.1992 15.4279 31.1513 15.4432V15.4785C30.9655 15.6093 30.8078 15.7758 30.6872 15.9683C30.5666 16.1608 30.4856 16.3754 30.4489 16.5996C30.4123 16.8238 30.4207 17.053 30.4737 17.2739C30.5266 17.4948 30.6231 17.7029 30.7575 17.886V17.892V17.901C30.7763 17.9167 30.7928 17.9317 30.8093 17.9467C30.8471 17.9938 30.8874 18.0389 30.93 18.0817C31.0302 18.2131 31.1006 18.3647 31.1364 18.5259C31.1721 18.6872 31.1724 18.8543 31.1372 19.0157C31.1019 19.1771 31.032 19.3289 30.9323 19.4605C30.8325 19.5922 30.7053 19.7006 30.5595 19.7782C30.531 19.7782 30.5018 19.7782 30.4718 19.7782C30.1964 19.7778 29.9251 19.8438 29.6807 19.9707C29.4363 20.0975 29.2262 20.2815 29.0681 20.5069C28.9099 20.7323 28.8085 20.9925 28.7724 21.2654C28.7364 21.5384 28.7667 21.816 28.8608 22.0747L28.8353 22.1122L28.8195 22.1355C28.892 22.3091 28.9205 22.4979 28.9024 22.6852C28.8844 22.8725 28.8205 23.0524 28.7162 23.209C28.612 23.3657 28.4707 23.4941 28.3049 23.583C28.1391 23.672 27.9539 23.7186 27.7658 23.7187C27.7136 23.7188 27.6615 23.7153 27.6098 23.7082C27.3427 23.6368 27.0624 23.631 26.7926 23.6913C26.5228 23.7516 26.2716 23.8762 26.0604 24.0545C25.8492 24.2329 25.6843 24.4597 25.5796 24.7155C25.475 24.9714 25.4338 25.2488 25.4595 25.524C25.4414 25.8139 25.3135 26.086 25.1019 26.285C24.8902 26.4839 24.6107 26.5948 24.3203 26.595C24.1979 26.5951 24.0764 26.5756 23.9603 26.5372C23.7317 26.3215 23.4483 26.1727 23.141 26.1071C22.8337 26.0414 22.5142 26.0614 22.2175 26.1648C21.9208 26.2683 21.6581 26.4512 21.4582 26.6937C21.2584 26.9362 21.1289 27.229 21.084 27.54H21.0773C20.9818 27.733 20.834 27.8952 20.6507 28.0082C20.4674 28.1212 20.2561 28.1804 20.0408 28.179L20.0393 28.1797Z\" fill=\"#021327\"/>\n<path d=\"M17.8212 11.162C17.3301 12.2471 17.0099 13.4015 16.8717 14.5845C16.8032 16.4263 17.8373 17.505 18.8177 17.301C19.8103 17.0882 20.3222 15.5299 19.9637 13.8005C19.8481 13.1526 19.6081 12.5333 19.2572 11.9767C19.9395 12.159 20.5502 12.5446 21.0083 13.0822C21.5763 13.5628 21.9747 14.2133 22.1445 14.9377C22.1445 14.9377 20.7289 13.9363 20.7818 14.4254C21.0667 17.0504 19.9542 19.4135 18.3463 19.642C16.6703 19.8891 15.1366 18.1177 15.529 15.2454C15.803 13.7274 16.3455 12.2705 17.131 10.943C17.3104 10.4285 17.3934 9.88528 17.3759 9.34065C17.3759 9.34065 17.446 8.80822 18.2488 9.03322C19.0741 9.26258 18.8635 9.72883 18.8635 9.72883C18.4453 10.1509 18.0938 10.6341 17.8212 11.162Z\" fill=\"#021327\"/>\n</g>\n</g>\n<defs>\n<clipPath id=\"clip0_410_487\">\n<rect width=\"36\" height=\"36\" fill=\"white\"/>\n</clipPath>\n</defs>\n</svg>",
              "title_image_class_width": "w-auto",
              "title_image_class_width_desktop": "lg:w-auto",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "1-Year Warranty",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-item",
              "text_item_1_class_type_size": "type--sm",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "@include ButtonStyle",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "@include ButtonStyle",
              "button_3_class_size": ""
            }
          },
          "content_item_cw8793": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-1/3",
              "item_class_width_desktop": "lg:w-1/3",
              "item_class_aspect": "aspect-auto",
              "item_class_aspect_desktop": "lg:aspect-auto",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-center layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "w-auto",
              "media_class_width_desktop": "lg:w-auto",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-center layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "content_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-col",
              "text_stack_class_layout": "layout-center layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "gap-2xs",
              "text_stack_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "<svg width=\"37\" height=\"36\" viewBox=\"0 0 37 36\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g clip-path=\"url(#clip0_410_510)\">\n<mask id=\"mask0_410_510\" style=\"mask-type:luminance\" maskUnits=\"userSpaceOnUse\" x=\"0\" y=\"0\" width=\"37\" height=\"36\">\n<path d=\"M36.5 0H0.5V36H36.5V0Z\" fill=\"white\"/>\n</mask>\n<g mask=\"url(#mask0_410_510)\">\n<path d=\"M31.7525 8.2425C34.1863 11.3853 35.385 15.3105 35.1223 19.2768C34.8597 23.2431 33.1537 26.9759 30.3267 29.7703C27.4996 32.5646 23.7472 34.2271 19.7781 34.4437C15.809 34.6602 11.898 33.4159 8.78375 30.9458\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\"/>\n<path d=\"M5.438 27.7192C3.0035 24.5765 1.80417 20.6511 2.06656 16.6844C2.32895 12.7177 4.0349 8.98438 6.86215 6.18971C9.68941 3.39505 13.4422 1.73249 17.4117 1.5161C21.3812 1.29972 25.2925 2.54448 28.4068 5.01525\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\"/>\n<path d=\"M9.5885 13.0777C9.58815 12.9321 9.62634 12.7891 9.69918 12.663C9.77203 12.5369 9.87694 12.4324 10.0033 12.36L18.047 7.72874C18.1724 7.65633 18.3147 7.61821 18.4595 7.61821C18.6043 7.61821 18.7466 7.65633 18.872 7.72874L26.918 12.3592C27.0443 12.4316 27.1492 12.5362 27.2221 12.6622C27.2949 12.7883 27.3331 12.9314 27.3328 13.077V22.3357C27.3331 22.4813 27.2949 22.6244 27.2221 22.7505C27.1492 22.8766 27.0443 22.9811 26.918 23.0535L18.8743 27.6855C18.7488 27.7579 18.6066 27.796 18.4618 27.796C18.3169 27.796 18.1747 27.7579 18.0493 27.6855L10.0033 23.0542C9.87694 22.9818 9.77203 22.8773 9.69918 22.7512C9.62634 22.6252 9.58815 22.4821 9.5885 22.3365L9.5885 13.0777Z\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\"/>\n<path d=\"M9.95825 12.8002L18.461 17.4307L26.9173 12.8242\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\"/>\n<path d=\"M13.7405 10.386L22.6033 15.222V18.3892\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\"/>\n<path d=\"M18.461 17.4307V27.4987\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\"/>\n<path d=\"M31.1248 11.3031V7.10339L34.8867 9.15674\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n<path d=\"M6.005 24.5281V28.7281L2.24301 26.6744\" stroke=\"#021327\" stroke-width=\"1.5\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n</g>\n</g>\n<defs>\n<clipPath id=\"clip0_410_510\">\n<rect width=\"36\" height=\"36\" fill=\"white\" transform=\"translate(0.5)\"/>\n</clipPath>\n</defs>\n</svg>",
              "title_image_class_width": "w-auto",
              "title_image_class_width_desktop": "lg:w-auto",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "Free Exchanges",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-item",
              "text_item_1_class_type_size": "type--sm",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "@include ButtonStyle",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "@include ButtonStyle",
              "button_3_class_size": ""
            }
          },
          "content_item_Lz4qjD": {
            "type": "content-item",
            "settings": {
              "item_class_width": "w-1/3",
              "item_class_width_desktop": "lg:w-1/3",
              "item_class_aspect": "aspect-auto",
              "item_class_aspect_desktop": "lg:aspect-auto",
              "item_class_visibility": "",
              "item_class_display": "flex",
              "item_class_display_desktop": "lg:flex",
              "item_class_direction": "flex-col",
              "item_class_layout": "layout-top",
              "item_class_layout_spacing": "layout-space-packed",
              "item_class_vertical_padding": "@include Spacing prop:py",
              "item_class_horizontal_padding": "@include Spacing prop:px",
              "item_class_gap": "@include Spacing prop:gap",
              "item_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "item_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "item_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "item_style_background": "",
              "content_style_background": "",
              "link": "",
              "liquid_link": "",
              "image_class_position": "",
              "image_loading": "eager",
              "media_class_width": "container",
              "media_class_width_desktop": "lg:container",
              "video": "",
              "videomobile": "",
              "video_class_position": "",
              "videomobile_class_position": "",
              "video_attr_autoplay": true,
              "video_attr_muted": true,
              "video_attr_loop": true,
              "video_attr_controls": false,
              "video_attr_playsinline": true,
              "loading_method": "scroll",
              "loading_delay": 2,
              "content_class_width": "w-full",
              "content_class_width_desktop": "lg:w-full",
              "content_class_display": "flex",
              "content_class_display_desktop": "lg:flex",
              "content_class_direction": "flex-col",
              "content_class_layout": "layout-top",
              "content_class_layout_spacing": "layout-space-packed",
              "content_class_vertical_padding": "@include Spacing prop:py",
              "content_class_horizontal_padding": "@include Spacing prop:px",
              "content_class_gap": "@include Spacing prop:gap",
              "content_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "content_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "content_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "content_class_aspect": "aspect-auto",
              "content_class_aspect_desktop": "lg:aspect-auto",
              "text_stack_class_width": "w-auto",
              "text_stack_class_width_desktop": "lg:w-auto",
              "text_stack_class_display": "flex",
              "text_stack_class_display_desktop": "lg:flex",
              "text_stack_class_direction": "flex-col",
              "text_stack_class_layout": "layout-center layout-top",
              "text_stack_class_layout_spacing": "layout-space-packed",
              "text_stack_class_vertical_padding": "@include Spacing prop:py",
              "text_stack_class_horizontal_padding": "@include Spacing prop:px",
              "text_stack_class_gap": "gap-2xs",
              "text_stack_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "text_stack_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "text_stack_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "svg": "<svg xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 36 36\" height=\"36\" width=\"36\">\n<g clip-path=\"url(#clip0_6031_2750)\" id=\"ico-shipping 1\">\n<g id=\"Clip path group\">\n<mask height=\"36\" width=\"36\" y=\"0\" x=\"0\" maskUnits=\"userSpaceOnUse\" style=\"mask-type:luminance\" id=\"mask0_6031_2750\">\n<g id=\"clip-path\">\n<path fill=\"white\" d=\"M36 0H0V36H36V0Z\" id=\"Rectangle 383\"/>\n</g>\n</mask>\n<g mask=\"url(#mask0_6031_2750)\">\n<g id=\"ico-shipping\">\n<g id=\"ico-free_shipping\">\n<path stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M6.38794 20.6319L6.38794 25.2346C6.38791 25.4666 6.44905 25.6945 6.56519 25.8953C6.68132 26.0961 6.84836 26.2627 7.04944 26.3784L19.8624 33.7539C20.0627 33.8693 20.2898 33.93 20.5209 33.93C20.7521 33.93 20.9792 33.8693 21.1794 33.7539L33.9932 26.3784C34.1943 26.2627 34.3613 26.0961 34.4774 25.8953C34.5936 25.6945 34.6547 25.4666 34.6547 25.2346V10.4844C34.6547 10.2524 34.5936 10.0245 34.4774 9.82374C34.3613 9.62294 34.1943 9.4563 33.9932 9.34065L21.1787 1.9629C20.9784 1.84752 20.7513 1.7868 20.5202 1.7868C20.2891 1.7868 20.062 1.84752 19.8617 1.9629L7.04944 9.34065C6.84836 9.4563 6.68132 9.62294 6.56519 9.82374C6.44905 10.0245 6.38791 10.2524 6.38794 10.4844V14.8899\" id=\"Path 8275\"/>\n<path stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M6.97656 10.0418L20.5208 17.4188L33.9916 10.0823\" id=\"Path 8276\"/>\n<path stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M13.4055 6.25426L27.1185 13.8998V18.945\" id=\"Path 8277\"/>\n<path stroke-dasharray=\"4 3\" stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M1.34033 15.516L9.01433 19.305\" id=\"Path 8279\"/>\n<path stroke-dasharray=\"4 3\" stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M2.81567 13.5263L8.12792 16.2263\" id=\"Path 8280\"/>\n<path stroke-dasharray=\"4 3\" stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M4.80225 19.671L7.695 21.0645\" id=\"Path 8282\"/>\n<path stroke-linecap=\"round\" stroke-width=\"1.5\" stroke=\"#021327\" d=\"M20.52 17.4187V33.456\" id=\"Path 8284\"/>\n<path stroke-linecap=\"round\" stroke=\"#021327\" d=\"M13.6785 21.2137L18.2535 23.7037\" id=\"Path 8278\"/>\n<path stroke-linecap=\"round\" stroke=\"#021327\" d=\"M15.3938 23.4668L18.2535 25.023\" id=\"Path 8281\"/>\n<path stroke-linejoin=\"round\" stroke-linecap=\"round\" stroke=\"#021327\" d=\"M18.2505 21.504L15.6787 20.103V17.448L18.2505 18.8677V21.504Z\" id=\"Path 8283\"/>\n</g>\n</g>\n</g>\n</g>\n</g>\n<defs>\n<clipPath id=\"clip0_6031_2750\">\n<rect fill=\"white\" height=\"36\" width=\"36\"/>\n</clipPath>\n</defs>\n</svg>",
              "title_image_class_width": "w-auto",
              "title_image_class_width_desktop": "lg:w-auto",
              "text_stack_class": "text-center",
              "content_style_color": "",
              "text_item_1_text": "Fast Fulfillment",
              "text_item_1_liquid": "",
              "text_item_1_attr_x_text": "",
              "text_item_1_element": "p",
              "text_item_1_class_type_style": "type-item",
              "text_item_1_class_type_size": "type--sm",
              "text_item_1_style_color": "",
              "text_item_2_text": "",
              "text_item_2_liquid": "",
              "text_item_2_attr_x_text": "",
              "text_item_2_element": "p",
              "text_item_2_class_type_style": "@include TypeStyle",
              "text_item_2_class_type_size": "@include TypeSize",
              "text_item_2_style_color": "",
              "text_item_3_text": "",
              "text_item_3_liquid": "",
              "text_item_3_attr_x_text": "",
              "text_item_3_element": "p",
              "text_item_3_class_type_style": "@include TypeStyle",
              "text_item_3_class_type_size": "@include TypeSize",
              "text_item_3_style_color": "",
              "liquid": "",
              "buttons__display": "flex",
              "buttons__display_desktop": "lg:flex",
              "buttons__direction": "flex-row",
              "buttons__layout": "layout-top",
              "buttons__layout_spacing": "layout-space-packed",
              "buttons__vertical_padding": "@include Spacing prop:py",
              "buttons__horizontal_padding": "@include Spacing prop:px",
              "buttons__gap": "@include Spacing prop:gap",
              "buttons__vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "buttons__horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "buttons__gap_desktop": "@include SpacingDesktop prop:gap",
              "button_1_text": "",
              "button_1_leading_icon": "",
              "button_1_trailing_icon": "",
              "button_1_link": "",
              "button_1_liquid_link": "",
              "button_1_onclick": "",
              "button_1_class_style": "@include ButtonStyle",
              "button_1_class_size": "",
              "button_2_text": "",
              "button_2_leading_icon": "",
              "button_2_trailing_icon": "",
              "button_2_link": "",
              "button_2_liquid_link": "",
              "button_2_onclick": "",
              "button_2_class_style": "@include ButtonStyle",
              "button_2_class_size": "",
              "button_3_text": "",
              "button_3_leading_icon": "",
              "button_3_trailing_icon": "",
              "button_3_link": "",
              "button_3_liquid_link": "",
              "button_3_onclick": "",
              "button_3_class_style": "@include ButtonStyle",
              "button_3_class_size": ""
            }
          },
          "break_FpE6FF": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "break_njhaec": {
            "type": "break",
            "settings": {
              "title": "⤶ Empty State"
            }
          },
          "2281212c-bd71-4dee-9195-3fe1892b4cc2": {
            "type": "line_items",
            "settings": {
              "empty_state": "",
              "item_badges": "item.properties._source=='GWP'::Free Gift::#021327::#EDFED1\nitem.properties._tags.includes('just in')::New Arrival::#021327::#EDFED1\nitem.properties._tags.includes('best selling')::Best Seller::#021327::#EDFED1\nitem.properties._tags.includes('trending')::Trending Now::#021327::#EDFED1\nitem.properties._tags.includes('low stock')::Low Stock::#021327::#EDFED1\nitem.properties._tags.includes('online only')::Online Only Exclusive::#021327::#EDFED1\nitem.properties._tags.includes('free shipping')::Free Shipping::#021327::#EDFED1\nitem.properties._tags.includes('free expedited shipping')::Free Fast Shipping::#021327::#EDFED1",
              "badge_position": "bottom_option",
              "line_item_title_source": "item.title.split(' - ')[0]",
              "hide_second_option": false,
              "show_item_type": true,
              "show_combined_options": true,
              "line_item_type_source": "item.product_type",
              "show_gift_card_reviews": false,
              "show_offer_options": false,
              "show_offer_price": false
            }
          },
          "4e8c02f8-fb9c-46d7-b556-f189a1518221": {
            "type": "product_recs",
            "disabled": true,
            "settings": {
              "title": "You May Also Like",
              "url": "/collections/olukai-cart-upsell/products.json",
              "include_cart_recs": true,
              "inclusion_js": "",
              "map": "",
              "enhanced_upsell_item": true,
              "include_swatches": true,
              "include_review_summary": true,
              "slides_per_view": 1.2,
              "slides_space_between": 8,
              "slides_per_view_desktop": 1.4,
              "slides_space_between_desktop": 8,
              "arrows": true,
              "loop": true
            }
          },
          "product_recs_hgNENd": {
            "type": "product_recs",
            "settings": {
              "title": "Explore Other Trending Styles",
              "url": "/collections/olukai-cart-upsell/products.json",
              "include_cart_recs": false,
              "inclusion_js": "",
              "map": "",
              "enhanced_upsell_item": true,
              "include_swatches": true,
              "include_review_summary": true,
              "slides_per_view": 1.2,
              "slides_space_between": 8,
              "slides_per_view_desktop": 1.4,
              "slides_space_between_desktop": 8,
              "arrows": true,
              "loop": true
            }
          },
          "frame_qVAQa9": {
            "type": "frame",
            "settings": {
              "title": "⤷ Footer",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "$store.cart.items.length > 0",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "#ffffff",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "py-md",
              "article_class_horizontal_padding": "px-md",
              "article_class_gap": "gap-md",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "sticky bottom-0 left-0 z-10 border-t"
            }
          },
          "offer_progress_summary_kUyg6Y": {
            "type": "offer_progress_summary",
            "settings": {
              "offer_combine_key": "ship",
              "threshold_icon_style_background_color": "#91ca6b",
              "threshold_icon_style_color": "#ffffff",
              "threshold_label_style_color": "#91ca6b"
            }
          },
          "frame_rqeDBP": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-top",
              "article_class_layout_spacing": "layout-space-between",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "text_ej36PK": {
            "type": "text",
            "settings": {
              "text_text": "Summary",
              "text_liquid": "",
              "text_attr_x_text": "",
              "text_element": "h5",
              "text_class_type_style": "type-subline",
              "text_class_type_size": "",
              "text_style_color": ""
            }
          },
          "text_wx3Gt4": {
            "type": "text",
            "settings": {
              "text_text": "",
              "text_liquid": "",
              "text_attr_x_text": "money.format($store.cart.total_price).split('.')[0]",
              "text_element": "h5",
              "text_class_type_style": "type-subline",
              "text_class_type_size": "",
              "text_style_color": ""
            }
          },
          "break_rewaVL": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "button_MHwLWG": {
            "type": "button",
            "settings": {
              "style": "button--primary",
              "button_class_size": "button button--large",
              "button_text": "CHECKOUT",
              "leading_icon": "",
              "trailing_icon": "",
              "link": "/checkout",
              "onclick": "",
              "onhover": "",
              "onclick_type": "x-data @",
              "form_validity": false,
              "button_class_custom_classes": "",
              "button_attr_x_data": "",
              "button_attr_x_show": "",
              "button_attr_x_text": "",
              "button_attr_QQclass": "",
              "disabled": false
            }
          },
          "frame_YcqKLw": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "max-lg:hidden",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-col",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": ""
            }
          },
          "title_gHaArw": {
            "type": "title",
            "settings": {
              "title_text": "1-YEAR WARRANTY - FAST FULFILLMENT - EXCEPTIONAL SERVICE",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "div",
              "title_class_type_style": "type-eyebrow",
              "title_class_type_size": "",
              "title_style_color": ""
            }
          },
          "break_mXBah6": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "frame_QjJCYq": {
            "type": "frame",
            "settings": {
              "title": "⤷ Frame",
              "article_class_visibility": "lg:hidden",
              "inclusion_liquid": "true",
              "inclusion_js": "",
              "article_class_width": "w-full",
              "article_class_width_desktop": "lg:w-full",
              "article_class_height": "h-auto",
              "article_class_height_desktop": "lg:h-auto",
              "article_style_background_color": "",
              "article_style_background": "",
              "article_class_display": "flex",
              "article_class_display_desktop": "lg:flex",
              "article_class_direction": "flex-row",
              "article_class_layout": "layout-center layout-middle",
              "article_class_layout_spacing": "layout-space-packed",
              "article_class_vertical_padding": "@include Spacing prop:py",
              "article_class_horizontal_padding": "@include Spacing prop:px",
              "article_class_gap": "@include Spacing prop:gap",
              "article_class_vertical_padding_desktop": "@include SpacingDesktop prop:py",
              "article_class_horizontal_padding_desktop": "@include SpacingDesktop prop:px",
              "article_class_gap_desktop": "@include SpacingDesktop prop:gap",
              "article_class_custom_classes": "eyebrow-smaller"
            }
          },
          "title_9zFEkQ": {
            "type": "title",
            "settings": {
              "title_text": "1-YEAR WARRANTY - FAST FULFILLMENT - EXCEPTIONAL SERVICE",
              "title_liquid": "",
              "title_attr_x_text": "",
              "title_element": "div",
              "title_class_type_style": "type-eyebrow",
              "title_class_type_size": "",
              "title_style_color": ""
            }
          },
          "break_4biyJa": {
            "type": "break",
            "settings": {
              "title": "⤶ Frame End"
            }
          },
          "8200c1b8-3e77-4ce6-9c03-044c9f17b0f7": {
            "type": "payment_widget",
            "disabled": true,
            "settings": {
              "payment_count": 4,
              "text": "[ count ] Interest-Free Payments of [ payment ] with ((shoppay 60x16)) or ((afterpay 72x24))",
              "link_1": "",
              "link_2": "",
              "link_3": "",
              "inclusion_js": ""
            }
          },
          "break_F3TNx6": {
            "type": "break",
            "settings": {
              "title": "⤶ Footer"
            }
          }
        },
        "block_order": [
          "frame_8Y9dfr",
          "frame_km84NC",
          "title_b3gB4H",
          "frame_UVUAcK",
          "button_hTWGcX",
          "break_FH9Vkb",
          "break_YA3mRz",
          "frame_mknhyD",
          "offer_progress_EP9gbN",
          "offer_progress_RGhxad",
          "break_NnkGyL",
          "break_FmTEdF",
          "frame_jUa8Xh",
          "title_9BWN8H",
          "frame_XkcdH4",
          "button_fPcKqF",
          "button_TpAJBn",
          "button_iK98rc",
          "button_j3jmpP",
          "break_t36VtN",
          "frame_ne3cjQ",
          "button_pFhqBU",
          "button_3PVK8Q",
          "button_Eqj7Qf",
          "button_ptQezj",
          "break_jAxyw6",
          "frame_znRA7w",
          "content_item_DWMFNE",
          "content_item_cw8793",
          "content_item_Lz4qjD",
          "break_FpE6FF",
          "break_njhaec",
          "2281212c-bd71-4dee-9195-3fe1892b4cc2",
          "4e8c02f8-fb9c-46d7-b556-f189a1518221",
          "product_recs_hgNENd",
          "frame_qVAQa9",
          "offer_progress_summary_kUyg6Y",
          "frame_rqeDBP",
          "text_ej36PK",
          "text_wx3Gt4",
          "break_rewaVL",
          "button_MHwLWG",
          "frame_YcqKLw",
          "title_gHaArw",
          "break_mXBah6",
          "frame_QjJCYq",
          "title_9zFEkQ",
          "break_4biyJa",
          "8200c1b8-3e77-4ce6-9c03-044c9f17b0f7",
          "break_F3TNx6"
        ],
        "custom_css": [
          ".modal--slider-cart {background-color: #f6f6f6;}",
          "p {color: #021327 !important;}",
          ".offer-summary__title {color: #021327 !important;}",
          "h5.type-subline.my-0 {color: #021327 !important;}",
          ".eyebrow-smaller .type-eyebrow {font-size: 11px !important;}"
        ],
        "settings": {
          "classes": "",
          "container_classes": "flex flex-col h-full"
        }
      },
      "redirects": {
        "type": "redirects",
        "settings": {}
      },
      "subnav": {
        "type": "subnav",
        "settings": {
          "section_classes": "hidden",
          "menu": "main-menu",
          "dynamic": true
        }
      },
      "search-modal": {
        "type": "search-modal",
        "blocks": {
          "e231a669-063a-4ec0-a9a1-df2235830d6c": {
            "type": "data-boost",
            "settings": {}
          }
        },
        "block_order": [
          "e231a669-063a-4ec0-a9a1-df2235830d6c"
        ],
        "settings": {
          "form": false,
          "debounce": 300,
          "limit": 9,
          "product_grid_classes": "w-3/4 p-5 mt-6 grid grid-cols-1 gap-y-10 gap-x-6 sm:grid-cols-2 lg:grid-cols-3 xl:gap-x-8",
          "product_filters_classes": "p-5 w-1/4 sticky top-24",
          "paging": "scroll",
          "sort": "featured:Featured\nbest-selling:Best Selling\nprice-ascending:Lowest Price",
          "search_item_title_logic": "product.title",
          "search_item_type_logic": "product.type",
          "search_item_sku_logic": "product.sku.split('|').at(0)",
          "show_search_suggestions": false,
          "search_suggestions": "backpack,bless up,hat,bag",
          "search_suggestions_heading_label": "Suggestions",
          "product_suggestions_type": "bestsellers",
          "product_list": [],
          "product_suggestions_heading_label": "Trending Products",
          "search_redirects": true,
          "search_redirects_map": "about:/pages/about\nstore:/pages/store-locator"
        }
      },
      "newsletter": {
        "type": "newsletter",
        "blocks": {
          "d9123f9a-1298-49a1-9454-75e6baac2533": {
            "type": "mini-form",
            "settings": {
              "inclusion_js": "true",
              "on_button_click": "",
              "form_method": "POST",
              "form_action": "",
              "prevent_default": true,
              "on_submit": "event.preventDefault();\nCustomer.identify('email', email.value); Customer.subscriptions.subscribe('news-and-offers','Footer Email Signup');\nklaviyo.identify({ '$email' : email.value });\n\nconst olukai_email_custom_source = 'Footer Email Sign Up for email'\nconst olukai_email_public_api = 'UNpuvd'\nconst olukai_email_list_id = 'XDgAjV'\n\nconst myHeaders = new Headers();myHeaders.append('revision', '2024-02-15');myHeaders.append('Content-Type', 'application/json');\nconst raw = JSON.stringify({'data': {'type': 'subscription','attributes': {'custom_source': olukai_email_custom_source,'profile': {'data': {'type': 'profile','attributes': {'email': email.value}}}},'relationships': {'list': {'data': {'type': 'list','id': olukai_email_list_id}}}}});\nconst requestOptions = {method: 'POST',headers: myHeaders,body: raw,redirect: 'follow'};\nfetch('https://a.klaviyo.com/client/subscriptions/?company_id='+olukai_email_public_api, requestOptions).then((response) => response.text()).then((result) => console.log('result',result)).catch((error) => console.error('error',error));",
              "field_type": "email",
              "trigger_text": "((mail 20x16)) SIGN UP FOR EMAILS",
              "field_name": "email",
              "placeholder_text": "Email Address",
              "input_pattern": "",
              "input_mask": "",
              "on_input": "",
              "activate_email_validation": false,
              "error_message": "",
              "submit_text": "SIGN UP",
              "button_style": "button--primary",
              "info_text": "<p>Get access to exclusive products and new arrival, color, and restock alerts when you sign up for emails.</p>",
              "success_text": "<p>We’ve sent you an email to verify <br/>your address.</p>"
            }
          },
          "9504fa8f-01a6-4101-b726-ae0bf78b234f": {
            "type": "mini-form",
            "settings": {
              "inclusion_js": "true",
              "on_button_click": "",
              "form_method": "POST",
              "form_action": "",
              "prevent_default": true,
              "on_submit": "Customer.identify('phone', phone.value); Customer.subscriptions.subscribe('sms_launches', 'Footer SMS Signup');",
              "field_type": "tel",
              "trigger_text": "((chat 20x16)) SIGN UP FOR TEXTS",
              "field_name": "phone",
              "placeholder_text": "Mobile Number",
              "input_pattern": "",
              "input_mask": "(999) - 999-9999",
              "on_input": "",
              "activate_email_validation": false,
              "error_message": "",
              "submit_text": "SIGN UP",
              "button_style": "button--primary",
              "info_text": "<p>Unlock EARLY access to our exclusive products when you sign up for texts.</p><p>By submitting this form, you agree to receive recurring automated and promotional marketing text messages (e.g. cart reminders) from OluKai at the cell number used when signing up. Reply HELP for help and STOP to cancel. Msg frequency varies. Msg & data rates apply. View <a href=\"https://olukai-store.myshopify.com/pages/messaging-terms-conditions\" target=\"_blank\" title=\"Messaging Terms & Conditions\">Terms</a> & <a href=\"https://olukai-store.myshopify.com/pages/messaging-privacy-policy\" target=\"_blank\" title=\"Messaging Privacy Policy\">Privacy</a>.</p>",
              "success_text": "<p>Reply “YES” to confirm your subscription.</p>"
            }
          }
        },
        "block_order": [
          "d9123f9a-1298-49a1-9454-75e6baac2533",
          "9504fa8f-01a6-4101-b726-ae0bf78b234f"
        ],
        "custom_css": [
          "@media only screen and (max-width: 1023px) {.newsletter__subtitle.type-subline {margin-bottom: 15px; } .newsletter__title {font-size: 36px; }}",
          ".button {background-color: #021327; border-color: #021327;}",
          "p {color: #fff;}",
          ".icon {color: #021327;}"
        ],
        "settings": {
          "wrapper_style_background_color": "#0066d2",
          "wrapper_style_background": "",
          "wrapper_class_vertical_padding": "py-xl",
          "wrapper_class_horizontal_padding": "",
          "wrapper_class_vertical_padding_desktop": "lg:py-2xl",
          "wrapper_class_horizontal_padding_desktop": "lg:px-6xl",
          "container_class_container": "container",
          "container_class_vertical_padding": "py-0",
          "container_class_horizontal_padding": "px-0",
          "container_class_vertical_padding_desktop": "lg:py-0",
          "container_class_horizontal_padding_desktop": "lg:px-0",
          "text_item_1_text": "Get On the List",
          "text_item_1_liquid": "",
          "text_item_1_attr_x_text": "",
          "text_item_1_element": "div",
          "text_item_1_class_type_style": "type-hero",
          "text_item_1_class_type_size": "type--lg",
          "text_item_1_style_color": "#ffffff",
          "text_item_2_text": "Choose how you want to hear from us.",
          "text_item_2_liquid": "",
          "text_item_2_attr_x_text": "",
          "text_item_2_element": "p",
          "text_item_2_class_type_style": "type-subline",
          "text_item_2_class_type_size": "",
          "text_item_2_style_color": "#ffffff"
        }
      },
      "offers": {
        "type": "offers",
        "blocks": {
          "offer_A4rGXN": {
            "type": "offer",
            "settings": {
              "title": "Free Exchanges",
              "key": "freeexchange",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 2000",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "2000",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're $${(2000 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} away from FREE exchanges!",
              "success": "Your bag qualifies for FREE exchanges",
              "threshold_icon": "<svg viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g>\n<path d=\"M17.3625 4.57916C18.7146 6.32516 19.3806 8.50583 19.2346 10.7093C19.0887 12.9128 18.1409 14.9866 16.5704 16.539C14.9998 18.0915 12.9151 19.015 10.7101 19.1354C8.50502 19.2557 6.33224 18.5644 4.60208 17.1921\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M2.74333 15.3996C1.39083 13.6536 0.72453 11.4728 0.870304 9.2691C1.01608 7.06538 1.96382 4.99134 3.53452 3.43874C5.10522 1.88615 7.19011 0.96251 9.39538 0.842294C11.6006 0.722078 13.7736 1.41361 15.5037 2.78626\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M5.72421 7.61765C5.72405 7.54784 5.74236 7.47923 5.77728 7.41879C5.81221 7.35835 5.86251 7.30822 5.92307 7.27351L9.77979 5.05297C9.83993 5.01825 9.90814 4.99998 9.97758 4.99998C10.047 4.99998 10.1152 5.01825 10.1754 5.05297L14.0332 7.27315C14.0937 7.30786 14.144 7.35799 14.1789 7.41843C14.2139 7.47887 14.2322 7.54748 14.232 7.61729V12.0566C14.2322 12.1264 14.2139 12.195 14.1789 12.2554C14.144 12.3159 14.0937 12.366 14.0332 12.4007L10.1764 14.6216C10.1163 14.6563 10.0481 14.6746 9.97865 14.6746C9.90922 14.6746 9.84101 14.6563 9.78087 14.6216L5.92307 12.4011C5.86251 12.3663 5.81221 12.3162 5.77728 12.2558C5.74236 12.1953 5.72405 12.1267 5.72421 12.0569L5.72421 7.61765Z\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M5.90149 7.48463L9.97829 9.7048L14.0328 7.49613\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M7.71497 6.32703L11.9644 8.64574V10.1643\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M9.9783 9.70476V14.532\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M17.0137 6.27949V3.94632L19.1037 5.08707\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n<path d=\"M3.05835 13.6267V15.9601L0.968353 14.8191\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n</g>\n</svg>",
              "threshold_label": "Free Exchanges"
            }
          },
          "79e1f2cf-c7b1-4bee-92d7-6f6c57c13a86": {
            "type": "offer",
            "settings": {
              "title": "Free Shipping",
              "key": "freeship",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 12000",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "12000",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're $${(12000 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} away from <strong>Free Shipping!</strong>",
              "success": "Your bag qualifies for FREE shipping!",
              "threshold_icon": "<svg viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g>\n<path d=\"M17.3625 4.57916C18.7146 6.32516 19.3806 8.50583 19.2346 10.7093C19.0887 12.9128 18.1409 14.9866 16.5704 16.539C14.9998 18.0915 12.9151 19.015 10.7101 19.1354C8.50502 19.2557 6.33224 18.5644 4.60208 17.1921\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M2.74333 15.3996C1.39083 13.6536 0.72453 11.4728 0.870304 9.2691C1.01608 7.06538 1.96382 4.99134 3.53452 3.43874C5.10522 1.88615 7.19011 0.96251 9.39538 0.842294C11.6006 0.722078 13.7736 1.41361 15.5037 2.78626\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M5.72421 7.61765C5.72405 7.54784 5.74236 7.47923 5.77728 7.41879C5.81221 7.35835 5.86251 7.30822 5.92307 7.27351L9.77979 5.05297C9.83993 5.01825 9.90814 4.99998 9.97758 4.99998C10.047 4.99998 10.1152 5.01825 10.1754 5.05297L14.0332 7.27315C14.0937 7.30786 14.144 7.35799 14.1789 7.41843C14.2139 7.47887 14.2322 7.54748 14.232 7.61729V12.0566C14.2322 12.1264 14.2139 12.195 14.1789 12.2554C14.144 12.3159 14.0937 12.366 14.0332 12.4007L10.1764 14.6216C10.1163 14.6563 10.0481 14.6746 9.97865 14.6746C9.90922 14.6746 9.84101 14.6563 9.78087 14.6216L5.92307 12.4011C5.86251 12.3663 5.81221 12.3162 5.77728 12.2558C5.74236 12.1953 5.72405 12.1267 5.72421 12.0569L5.72421 7.61765Z\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M5.90149 7.48463L9.97829 9.7048L14.0328 7.49613\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M7.71497 6.32703L11.9644 8.64574V10.1643\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M9.9783 9.70476V14.532\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M17.0137 6.27949V3.94632L19.1037 5.08707\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n<path d=\"M3.05835 13.6267V15.9601L0.968353 14.8191\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n</g>\n</svg>",
              "threshold_label": "Free Shipping"
            }
          },
          "offer_3kY8LR": {
            "type": "offer",
            "settings": {
              "title": "Free Fast Shipping",
              "key": "freefastship",
              "combinable": true,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 17500",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "17500",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're $${(17500 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} away from <strong>Free Fast Shipping!</strong>",
              "success": "Your bag qualifies for <strong>Free Fast Shipping!</strong>",
              "threshold_icon": "<svg viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g>\n<path d=\"M17.3625 4.57916C18.7146 6.32516 19.3806 8.50583 19.2346 10.7093C19.0887 12.9128 18.1409 14.9866 16.5704 16.539C14.9998 18.0915 12.9151 19.015 10.7101 19.1354C8.50502 19.2557 6.33224 18.5644 4.60208 17.1921\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M2.74333 15.3996C1.39083 13.6536 0.72453 11.4728 0.870304 9.2691C1.01608 7.06538 1.96382 4.99134 3.53452 3.43874C5.10522 1.88615 7.19011 0.96251 9.39538 0.842294C11.6006 0.722078 13.7736 1.41361 15.5037 2.78626\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M5.72421 7.61765C5.72405 7.54784 5.74236 7.47923 5.77728 7.41879C5.81221 7.35835 5.86251 7.30822 5.92307 7.27351L9.77979 5.05297C9.83993 5.01825 9.90814 4.99998 9.97758 4.99998C10.047 4.99998 10.1152 5.01825 10.1754 5.05297L14.0332 7.27315C14.0937 7.30786 14.144 7.35799 14.1789 7.41843C14.2139 7.47887 14.2322 7.54748 14.232 7.61729V12.0566C14.2322 12.1264 14.2139 12.195 14.1789 12.2554C14.144 12.3159 14.0937 12.366 14.0332 12.4007L10.1764 14.6216C10.1163 14.6563 10.0481 14.6746 9.97865 14.6746C9.90922 14.6746 9.84101 14.6563 9.78087 14.6216L5.92307 12.4011C5.86251 12.3663 5.81221 12.3162 5.77728 12.2558C5.74236 12.1953 5.72405 12.1267 5.72421 12.0569L5.72421 7.61765Z\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M5.90149 7.48463L9.97829 9.7048L14.0328 7.49613\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M7.71497 6.32703L11.9644 8.64574V10.1643\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M9.9783 9.70476V14.532\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M17.0137 6.27949V3.94632L19.1037 5.08707\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n<path d=\"M3.05835 13.6267V15.9601L0.968353 14.8191\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n</g>\n</svg>",
              "threshold_label": "Free Upgraded Shipping"
            }
          },
          "2dee28d1-3bf3-4db8-af78-12c1f0b2f954": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "GWP | Filtered Cart Total",
              "key": "gwp-item",
              "combinable": false,
              "inclusion": "{% assign today = \"now\" | date: \"%m-%d\" %}{% if '11-27,11-28,11-29' contains today %}1{% endif %}",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.filter(item=>!['OluKai Digital Gift Card','Maui Fire Relief Fund'].includes(item.product_title)).map(i=>i. final_line_price)) > 19999",
              "progress_numerator": "Util.math.sum(cart.items.filter(item=>!['OluKai Digital Gift Card','Maui Fire Relief Fund'].includes(item.product_title)).map(i=>i. final_line_price))",
              "progress_denominator": "20000",
              "action_filter": "!cart.items.some(i=>i.variant_id==**************)",
              "delay": 0,
              "actions": "Cart.add({\nid:**************,\nproperties:{\n_source:`GWP`\n}})",
              "revoke": "Cart.remove(**************)",
              "automatic": true,
              "storage": "local",
              "approach": "",
              "success": "",
              "threshold_icon": "",
              "threshold_label": ""
            }
          },
          "f733ba12-12f7-4af5-a338-3ec46f07de69": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "GWP | Bundle",
              "key": "gwp-bundle",
              "combinable": false,
              "inclusion": "{% assign today = \"now\" | date: \"%m-%d\" %}{% if '11-22,11-23,11-24,11-25,05-15' contains today %}1{% endif %}",
              "inclusion_js": "",
              "conditions": "cart.items.filter(i=>i.properties.Bundle=='Bundle').length >= 2",
              "progress_numerator": "",
              "progress_denominator": "",
              "action_filter": "",
              "actions": "",
              "revoke": "Cart.remove(cart.items.find(i=>i.product_title.includes('OluKai Shirt')).variant_id)",
              "automatic": true,
              "storage": "local",
              "approach": "",
              "success": "",
              "threshold_icon": "",
              "threshold_label": ""
            }
          },
          "offer_U6G9M7": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "Free Shipping",
              "key": "freeshipsolo",
              "combinable": false,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 25000",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "25000",
              "action_filter": "",
              "delay": 1,
              "actions": "",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "You're $${(25000 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} away from FREE shipping!",
              "success": "Your bag qualifies for FREE shipping!",
              "threshold_icon": "",
              "threshold_label": ""
            }
          },
          "offer_CytYex": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "Labor Day GWP",
              "key": "laborday24",
              "combinable": false,
              "inclusion": "{% assign today = \"now\" | date: \"%m-%d\" %}{% if '11-22,11-23,11-24,08-23,08-28,01-24,1-24,01-25,1-25' contains today %}1{% endif %}",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.filter(item=>!['OluKai Digital Gift Card','Maui Fire Relief Fund'].includes(item.product_title)).map(i=>i. final_line_price)) >= 17500",
              "progress_numerator": "",
              "progress_denominator": "",
              "action_filter": "!cart.items.some(i=>i.variant_id==**************)",
              "delay": 60,
              "actions": "Cart.add({\nid:7674022658147,\nproperties:{\n_source:`GWP`\n}})",
              "revoke": "",
              "automatic": true,
              "storage": "local",
              "approach": "",
              "success": "",
              "threshold_icon": "",
              "threshold_label": ""
            }
          },
          "offer_7bPhEd": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "BFCM GWP",
              "key": "gifts_test",
              "combinable": false,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 12000",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "40000",
              "action_filter": "1 == 1",
              "actions": "Cart.add({\nid:**************,\nproperties:{\n_source:`GWP`\n}})",
              "revoke": "Cart.remove(**************)",
              "automatic": true,
              "storage": "",
              "approach": "test",
              "success": "test success",
              "threshold_icon": "",
              "threshold_label": ""
            }
          },
          "5eed6bc5-7100-430d-b1f2-4c07a04157f9": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "GWP | Item Inclusion",
              "key": "gwp-total",
              "combinable": false,
              "inclusion": "1",
              "inclusion_js": "",
              "conditions": "Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price > 32500",
              "progress_numerator": "cart.total_price",
              "progress_denominator": "32500",
              "action_filter": "!cart.items.some(i=>i.variant_id==**************)",
              "delay": 1,
              "actions": "Cart.add({\nid:**************,\nproperties:{\n_source:`GWP`\n}})",
              "revoke": "Cart.remove(**************)",
              "automatic": true,
              "storage": "session",
              "approach": "You're $${(32500 - (Util.math.sum(cart.items.map(item => (item.product_type === 'Gift Card' || item.sku.includes('00972') || item.product_type.includes('Insole')) ? -item.price : 0)) + cart.total_price))/100} away from FREE GIFT!",
              "success": "Your bag qualifies for FREE GIFT!",
              "threshold_icon": "<svg viewBox=\"0 0 20 20\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n<g>\n<path d=\"M17.3625 4.57916C18.7146 6.32516 19.3806 8.50583 19.2346 10.7093C19.0887 12.9128 18.1409 14.9866 16.5704 16.539C14.9998 18.0915 12.9151 19.015 10.7101 19.1354C8.50502 19.2557 6.33224 18.5644 4.60208 17.1921\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M2.74333 15.3996C1.39083 13.6536 0.72453 11.4728 0.870304 9.2691C1.01608 7.06538 1.96382 4.99134 3.53452 3.43874C5.10522 1.88615 7.19011 0.96251 9.39538 0.842294C11.6006 0.722078 13.7736 1.41361 15.5037 2.78626\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M5.72421 7.61765C5.72405 7.54784 5.74236 7.47923 5.77728 7.41879C5.81221 7.35835 5.86251 7.30822 5.92307 7.27351L9.77979 5.05297C9.83993 5.01825 9.90814 4.99998 9.97758 4.99998C10.047 4.99998 10.1152 5.01825 10.1754 5.05297L14.0332 7.27315C14.0937 7.30786 14.144 7.35799 14.1789 7.41843C14.2139 7.47887 14.2322 7.54748 14.232 7.61729V12.0566C14.2322 12.1264 14.2139 12.195 14.1789 12.2554C14.144 12.3159 14.0937 12.366 14.0332 12.4007L10.1764 14.6216C10.1163 14.6563 10.0481 14.6746 9.97865 14.6746C9.90922 14.6746 9.84101 14.6563 9.78087 14.6216L5.92307 12.4011C5.86251 12.3663 5.81221 12.3162 5.77728 12.2558C5.74236 12.1953 5.72405 12.1267 5.72421 12.0569L5.72421 7.61765Z\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M5.90149 7.48463L9.97829 9.7048L14.0328 7.49613\" stroke=\"currentColor\" stroke-width=\"0.5\" stroke-linecap=\"round\"/>\n<path d=\"M7.71497 6.32703L11.9644 8.64574V10.1643\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M9.9783 9.70476V14.532\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\"/>\n<path d=\"M17.0137 6.27949V3.94632L19.1037 5.08707\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n<path d=\"M3.05835 13.6267V15.9601L0.968353 14.8191\" stroke=\"currentColor\" stroke-width=\"0.75\" stroke-linecap=\"round\" stroke-linejoin=\"round\"/>\n</g>\n</svg>",
              "threshold_label": "Free Gift"
            }
          },
          "offer_XaURtK": {
            "type": "offer",
            "disabled": true,
            "settings": {
              "title": "GWP | Bundle FD",
              "key": "gwp-bundle-fd",
              "combinable": false,
              "inclusion": "{% assign today = \"now\" | date: \"%m-%d\" %}{% if '11-22,11-23,05-14,05-15,05-16,05-17,05-18,05-19' contains today %}1{% endif %}",
              "inclusion_js": "",
              "conditions": "Object.values(cart.items.reduce((acc, item) => {\n    if (item.properties && item.properties.Bundle) {\n      const bundleKey = item.properties.Bundle;\n      if (!acc[bundleKey]) {\n        acc[bundleKey] = { primary: 0, shirt: 0 };\n      }\n      if (item.title.startsWith(\"OluKai Shirt\")) {\n        acc[bundleKey].shirt += item.quantity;\n      } else {\n        acc[bundleKey].primary += item.quantity;\n      }\n    }\n    return acc;\n  }, {})).every(bundle => bundle.primary >= bundle.shirt && bundle.primary % 1 === 0 && bundle.shirt % 1 === 0)",
              "progress_numerator": "",
              "progress_denominator": "",
              "action_filter": "",
              "actions": "",
              "revoke": "Object.values(cart.items.reduce((acc,item)=>{if(item.properties&&item.properties.Bundle){const bundleKey=item.properties.Bundle;if(!acc[bundleKey]){acc[bundleKey]={primary:0,shirt:0,shirtItems:[]};}if(item.title.startsWith(\"OluKai Shirt\")){acc[bundleKey].shirt+=item.quantity;acc[bundleKey].shirtItems.push({id:item.id,quantity:item.quantity});}else{acc[bundleKey].primary+=item.quantity;}}return acc;},{})).forEach(bundle=>{console.log('Checking bundle:',bundle);if(bundle.shirt>bundle.primary){console.log('Shirt quantity exceeds primary item quantity.');let excessShirts=bundle.shirt-bundle.primary;bundle.shirtItems.forEach(shirt=>{if(excessShirts>0){let adjustment=Math.min(shirt.quantity,excessShirts);console.log('Adjusting shirt quantity by ' + adjustment);console.log('cart update', { [shirt.id]: shirt.quantity - adjustment });Cart.update({[shirt.id]:shirt.quantity-adjustment}).then(response=>{console.log('Adjusted shirt quantity by '+adjustment,response);});excessShirts-=adjustment;}});}})",
              "automatic": true,
              "storage": "local",
              "approach": "",
              "success": "",
              "threshold_icon": "",
              "threshold_label": ""
            }
          }
        },
        "block_order": [
          "offer_A4rGXN",
          "79e1f2cf-c7b1-4bee-92d7-6f6c57c13a86",
          "offer_3kY8LR",
          "2dee28d1-3bf3-4db8-af78-12c1f0b2f954",
          "f733ba12-12f7-4af5-a338-3ec46f07de69",
          "offer_U6G9M7",
          "offer_CytYex",
          "offer_7bPhEd",
          "5eed6bc5-7100-430d-b1f2-4c07a04157f9",
          "offer_XaURtK"
        ],
        "settings": {}
      },
      "geolocation": {
        "type": "geolocation",
        "settings": {
          "heading": "",
          "currency_selector": false,
          "language_selector": false,
          "site_switcher": false,
          "site_list": false,
          "button_text": "Change Settings",
          "show_flag": "yes",
          "flag": "https://flagcdn.com/216x162",
          "geoip": "https://get.geojs.io/v1/ip/country.json"
        }
      },
      "loyalty": {
        "type": "loyalty",
        "settings": {
          "title": ""
        }
      },
      "giftcard": {
        "type": "giftcard",
        "blocks": {
          "link_9RaqHk": {
            "type": "link",
            "settings": {
              "link_url": "shopify://collections/all-mens",
              "link_text": "SHOP MENS"
            }
          },
          "link_4eEFwe": {
            "type": "link",
            "settings": {
              "link_url": "shopify://collections/women-all",
              "link_text": "SHOP WOMENS"
            }
          }
        },
        "block_order": [
          "link_9RaqHk",
          "link_4eEFwe"
        ],
        "custom_css": [],
        "settings": {
          "logo": "shopify://shop_images/OluKai-Logo-Horizontal-Navy_1215px_1.jpg",
          "image": "shopify://shop_images/New-OluKai-Gift-Card_2_bd514997-521f-408c-8da6-adc5012913ed.png"
        }
      },
      "product-badges": {
        "type": "product-badges",
        "blocks": {
          "badge_aGUgmm": {
            "type": "badge",
            "settings": {
              "title": "New Arrival",
              "tag": "just in",
              "grid": "New Arrival",
              "detail": "New Arrival",
              "carousel": "New Arrival",
              "search": "New Arrival",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          },
          "badge_Cc49fX": {
            "type": "badge",
            "settings": {
              "title": "Best Seller",
              "tag": "best selling",
              "grid": "Best Seller",
              "detail": "Best Seller",
              "carousel": "Best Seller",
              "search": "Best Seller",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          },
          "badge_McdHep": {
            "type": "badge",
            "settings": {
              "title": "Trending Now",
              "tag": "trending",
              "grid": "Trending",
              "detail": "Trending",
              "carousel": "Trending",
              "search": "Trending",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          },
          "badge_pKU66r": {
            "type": "badge",
            "settings": {
              "title": "Low Stock",
              "tag": "low stock",
              "grid": "Low Stock",
              "detail": "Low Stock",
              "carousel": "Low Stock",
              "search": "Low Stock",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          },
          "badge_tmmPVN": {
            "type": "badge",
            "settings": {
              "title": "Online Only Exclusive",
              "tag": "online only",
              "grid": "Online-Only",
              "detail": "Online-Only",
              "carousel": "Online-Only",
              "search": "Online-Only",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          },
          "badge_AjiYmA": {
            "type": "badge",
            "settings": {
              "title": "Free Shipping",
              "tag": "free shipping",
              "grid": "Free Shipping",
              "detail": "Free Shipping",
              "carousel": "Free Shipping",
              "search": "Free Shipping",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          },
          "badge_UxHcHk": {
            "type": "badge",
            "settings": {
              "title": "Free Expedited Shipping",
              "tag": "free expedited shipping",
              "grid": "Free Expedited Shipping",
              "detail": "Free Expedited Shipping",
              "carousel": "Free Expedited Shipping",
              "search": "Free Expedited Shipping",
              "color": "#00213b",
              "bg_color": "#ffffff"
            }
          }
        },
        "block_order": [
          "badge_aGUgmm",
          "badge_Cc49fX",
          "badge_McdHep",
          "badge_pKU66r",
          "badge_tmmPVN",
          "badge_AjiYmA",
          "badge_UxHcHk"
        ],
        "settings": {}
      },
      "customer-segments": {
        "type": "customer-segments",
        "settings": {}
      }
    },
    "content_for_index": [],
    "blocks": {
      "16274361319697914395": {
        "type": "shopify://apps/reviews-io/blocks/reviewsio-floating/92a082e5-b297-4e88-b428-ac96cbc505f9",
        "disabled": false,
        "settings": {
          "widget_id": "",
          "show_all": true,
          "show_index": true,
          "show_product": true,
          "show_collection": true,
          "show_cart": true
        }
      },
      "10830403595879607806": {
        "type": "shopify://apps/reviews-io/blocks/reviewsio-core/92a082e5-b297-4e88-b428-ac96cbc505f9",
        "disabled": false,
        "settings": {
          "url_key": "olukai",
          "lang": "auto"
        }
      },
      "15316669992541710076": {
        "type": "shopify://apps/reviews-io/blocks/reviewsio-rating-snippet/92a082e5-b297-4e88-b428-ac96cbc505f9",
        "disabled": false,
        "settings": {
          "star": "#f1a307",
          "text_color": "#381300",
          "snippet_mode": "default",
          "text": "Reviews",
          "line_break": false,
          "listen_for_changes": false,
          "rating_without_brackets": false,
          "format_review_count": false,
          "show_popup": false,
          "anchor_to_reviews_widget": true,
          "show_empty_stars": true
        }
      },
      "855628211100114053": {
        "type": "shopify://apps/klaviyo-email-marketing-sms/blocks/klaviyo-onsite-embed/2632fe16-c075-4321-a88b-50b567f42507",
        "disabled": false,
        "settings": {}
      }
    }
  },
  "type": "template",
  "platform_customizations": {
    "custom_css": []
  }
}
