const Products = {

  /**
   * Sets the product object.
   * @param {Object} p - The product object.
   * @returns {Promise} Resolves to the product object.
   */

  init: (handle) => {
    window.newmedia = [];
    if(!handle && document.location.pathname.includes('/products/')) handle = document.location.pathname.split('/').reverse()[0]

    const product = products[handle]

    return new Promise(res => {

      Products.enhance(handle).then(p=>{

        if (!!product.preselect_variant) {
          Products.select.variant(handle);

          setTimeout(()=>{
            Util.events.dispatch('Products:variantSelected', product.variant||false)
          },100)
        }

        if (!!product.preselect_option) 
           Products.select.option(handle, product.options[0].name, product.variants[0].option1);

        Util.events.dispatch('Products:init', product)

        if (document.location.pathname.endsWith(`/${handle}`) || document.location.pathname.endsWith(`/products/${handle}`)) {

          setTimeout(()=>{
            Util.events.dispatch('Products:initialized', product)
          },100)
        }
        
        res(product)

      })

    })
  },

  set: p => {
    return new Promise((res, rej) => {

      res(product)

    })

  },

  refresh: handle => {
    window.products[handle].refreshed = new Date().toISOString();
    Util.events.dispatch('Products:refresh')

  },


  /**
   * @method Products.enhance
   * Enhances the product object with options and availability information.
   * @param {string} handle - The product handle.
   * @returns {Promise} Resolves to the enhanced product object.
   */

  enhance: handle => {
    handle = handle || document.location.pathname.split('/').at(-1)

    const product = products[handle]

    return new Promise((res, rej) => {

      product.options = product.options_with_values.map((o, i) => {
        return {
          name: o.name || o,
          values: product.variants
            .map(v => v[`option${i+1}`])
            .filter(
              (value, index, array) => array.indexOf(value) === index
            )
            .map(value => {
              const variants = product.variants.filter(v => v.options[i] == value)
              const availability = {
                quantity: Util.math.sum(variants.map(v => v.inventory_quantity)),
                available: variants.some(v => v.available)
              }
              return {
                value: value,
                image: !!variants[0].featured_image ? variants[0].featured_image : products[variants[0].product].images[0],
                product: variants[0].product,
                availability: {
                  initial: availability,
                  cascade: availability,
                  progressive: availability,
                  reflexive: availability
                }
              }
            })
        }
      })

      product.selected_options = !!product.selected_options ? product.selected_options.map((value, i) => { return { name: product.options[i].name, value: value } }) : [];

      res(product)

    })
  },

  isbundle: handle => {
    if (window.selectedValues.length === 0) {
      let productColors = {};
      for (let key in window.products) {
          if (key !== window.isBundleProducthandle) {
              let product = window.products[key];
              let productType = product.product_type;
              let color = product.color;
              if (!productColors[productType]) {
                  productColors[productType] = [];
              }
              productColors[productType].push(color);
          }
      }
      let selectedValuesArray = [];
      for (let productType in productColors) {
          if (productColors[productType].length > 0) {
              selectedValuesArray.push(productColors[productType][0]);
          }
      }
      selectedValuesArray.reverse();
      window.selectedValues = selectedValuesArray;
    }
    const product = products[handle];
    return new Promise((res, rej) => {
        product.newmedia = [];
        Object.entries(window.products).forEach(([key, productItem]) => {
            if (key === window.isBundleProducthandle) {
                let medias = productItem.media;
                // let reversedMedias = medias.slice().reverse();
                let reversedMedias = medias;
                let selectedCounts = {};
                Object.values(window.selectedValues).forEach(selectedValue => {
                  selectedCounts[selectedValue] = 0;
                });
                reversedMedias.forEach(media => {
                    let mediaAlt = media.alt;
                    if (mediaAlt != null) {
                        Object.values(window.selectedValues).forEach(selectedValue => {
                            if (mediaAlt.replace(/\d+/g, '').trim() === selectedValue && selectedCounts[selectedValue] < 4) {
                              product.newmedia.push(media);
                              selectedCounts[selectedValue]++;
                            }
                        });
                    }
                });
                product.newmedia = product.newmedia.filter(media => {
                    let mediaAlt = media.alt;let mediaCount = {};let isValid = true;
                    if (mediaAlt != null) {
                        Object.values(window.selectedValues).forEach(selectedValue => {
                            if (mediaAlt.indexOf(selectedValue) !== -1) {
                                if (!mediaCount[selectedValue]) {mediaCount[selectedValue] = 0;}
                                mediaCount[selectedValue]++;
                                if (mediaCount[selectedValue] > 400) {isValid = false;}
                            }
                        });
                    }
                    return isValid;
                });
                const extractCommonText = (str) => {return str.replace(/\d+/g, '').trim();};
                const groupedMedia = product.newmedia.reduce((acc, media) => {
                    const commonText = extractCommonText(media.alt);
                    if (!acc[commonText]) {acc[commonText] = [];}
                    acc[commonText].push(media);
                    return acc;
                }, {});
                Object.keys(groupedMedia).forEach(key => {
                  groupedMedia[key].sort((a, b) => {
                    const numA = parseInt(a.alt.match(/\d+/)[0], 10);
                    const numB = parseInt(b.alt.match(/\d+/)[0], 10);
                    return numA - numB;
                  });
                });
                product.newmedia = Object.values(groupedMedia).flat();
            }
        });
        res(product);
    });
},


  select: {    
    /**
     * Selects a product variant.
     * @param {number} id - The variant ID.
     * @param {string} handle - The product handle.
     * @returns {Promise} Resolves to the selected product variant.
     */

    variant: (handle, id) => {
      handle = handle || document.location.pathname.split('/').at(-1)

      const product = products[handle]

      return new Promise((res, rej) => {

        product.variant = product.variants.find(v => {
          return (
            (!id && v.available) ||
            v.id == id
          )
        })
        product.selected_options = product.variant.options.map((value, i) => { return { name: product.options[i].name, value: value, index: i } })
        product.options.map((o, i) => {
          o.selected_value = product.variant.options[i]
        })

        // Products.set(product).then((product) => res(product.variant))
        Util.events.dispatch('Products:variantSelected', product.variant||false)

        res(product.variant)

      })
    },


    /**
     * Selects a product option.
     * @param {string} key - The option key.
     * @param {string} value - The option value.
     * @param {string} handle - The product handle.
     * @returns {Promise} Resolves to the updated product options.
     */

    option: (handle, name, value) => {
      handle = handle || document.location.pathname.split('/').at(-1)
      const product = products[handle]
      return new Promise((res, rej) => {
        const selected_index = product.selected_options.findIndex(so => so.name === name)
        product.options.find(o => o.name === name).selected_value = value
        if(selected_index>-1)
          product.selected_options.splice(product.selected_options.findIndex(so => so.name === name), 1);  
          product.selected_options.push({ 
            name: name, 
            value: value, 
            index: product.options.findIndex(o => o.name == name),
            order: product.selected_options.reduce((max, option) => {
                      return Math.max(max, option.order || 0);
                  }, 0) + 1
          })  
          Products.availability.process(handle)

          const variant = product.variants.find(v=>{
            return v.options.every((o,i)=>{
              return (
                product.selected_options.find(s=>s.index==i) && 
                product.selected_options.find(s=>s.index==i).value == o
              )
            })
          })

          // Util.events.dispatch('Products:optionSelected', {...product.options.find(o=>o.name==name),...{product:handle}})
          
          // include the selected value object ^^^ 
          // then in the siblings script 
          // use that to check against the current url in the document.location
          // and if different load the images and replace the history state

          let variant_change = false
          if(variant != product.variant) variant_change = true;
          product.variant = variant

          // if(variant_change) Util.events.dispatch('Products:variantSelected', variant||false)
          if(variant_change) {
            Util.events.dispatch('Products:optionSelected', {...product.options.find(o=>o.name==name),...{product:handle}, variant:variant})
            Util.events.dispatch('Products:variantSelected', variant||false)
            if (product.history_state && variant_change){
              Util.urlparams.change('variant',variant.id)
            }

          } else {
            Util.events.dispatch('Products:optionSelected', {...product.options.find(o=>o.name==name),...{product:handle}})
          }
          if(window.isBundleProduct){
            if (name == 'Color') {
              Products.isbundle(window.isBundleProducthandle).then(product => {res(product)})
              Products.refresh(window.isBundleProducthandle).then(product => {res(product)})
            }
          }
          res(product)
          if(name == 'Color' || name == 'color'){
            if(document.querySelector('.product-essentials__media .swiper')){
              setTimeout(function () {
                document.querySelector('.product-essentials__media .swiper').swiper.slideTo(0)
              }, 300)
            }
          }
      })
    },

    /**
     * Selects a product based on a key-value pair.
     * @param {string} key - The key to match.
     * @param {string} value - The value to match.
     * @returns {Promise} Resolves to the selected product.
     */

    product: (key, value) => {  
      return new Promise((res, rej) => {

        const product = Object.values(window.products).find(p => {
          return (
            !key ||
            p[key] == value ||
            value.includes('*') && p[key].includes(value.replace('*', '')) ||
            (
              key.includes('variant.') &&
              p.variants.map(v => v[key.replace('variant.', '')]).includes(value)
            )
          )
        })
        Products.enhance(product.handle).then(product => {
          res(product)
          // Products.set(product).then((product) => res(product))
        })

      })
    }
  },

  availability: {

    process:(handle)=>{
      return new Promise((res, rej) => {

        Promise.all([
          Products.availability.cascade(handle),
          Products.availability.progressive(handle),
          Products.availability.reflexive(handle)
        ])
      })
    },

    /**
     * @method Product.availability.cascade()
     * Updates product options availability based on cascade selection.
     * @returns {Promise} Resolves to the updated product options.
     */

    cascade: (handle) => {  
      const product = products[handle]

      return new Promise((res, rej) => {

        product.options = product.options.map((option, oi) => {
          option.values.map(val => {
            const variants = product.variants.filter(v => {
              if (oi > 0 && v.options[0] != product.options[0].selected_value) return false;
              if (oi > 1 && v.options[1] != product.options[1].selected_value) return false;
              if (v.options[oi] != val.value) return false;
              return true;
            })
            val.availability.cascade = {
              quantity: Util.math.sum(variants.map(v => v.inventory_quantity)),
              available: variants.some(v => v.available)
            }
            return val;
          })
          return option;
        })

        // Products.set(product).then((product) => res(product.options))
        res(product.options)

      })

    },


    /**
     * @method Product.availability.progressive()
     * Updates product options availability based on progressive selection.
     * @returns {Promise} Resolves to the updated product options.
     */

    progressive: (handle) => {
      const product = products[handle]

      return new Promise((res, rej) => {

        product.options = product.options.map((option, oi) => {
          
          option.values.map(val => {
              const variants = product.variants.filter(v => {
                  if (v.options[oi] != val.value) return false;
                  
                  // Sort selected options by order and then check for availability
                  return product.selected_options
                    .filter(selected => selected.index !== oi)
                    .sort((a, b) => a.order - b.order)
                    .every(selected => {
                      return selected.value === v.options[selected.index];
                    });
              })
              val.availability.progressive = {
                  quantity: Util.math.sum(variants.map(v => v.inventory_quantity)),
                  available: variants.some(v => v.available)
              }
              return val;
          })
          return option;
        })

        res(product.options)

      })


    },

    reflexive: (handle) => {
      const product = products[handle];

      return new Promise((res, rej) => {

        product.options = Array.from(product.options).map((option, oi) => {

          option.values.map(val => {
            const variants = product.variants.filter(v => {
              if (v.options[oi] !== val.value) return false;

              // Only consider the last selected option (most recent) when checking for availability
              const mostRecentSelection = product.selected_options.sort((a, b) => b.order - a.order)[0];
              if (mostRecentSelection && mostRecentSelection.index !== oi) {
                return mostRecentSelection.value === v.options[mostRecentSelection.index];
              }
              return true;
            });

            val.availability.reflexive = {
                quantity: Util.math.sum(variants.map(v => v.inventory_quantity)),
                available: variants.some(v => v.available)
            }
            return val;
          });
          return option;
        });
        res(product.options);
      });
    }

  },


  /**
   * @method Product.availability.cascade()
   * Updates product options availability based on cascade selection.
   * @returns {Promise} Resolves to the updated product options.
   */
  combine: (handle) => {
    const product = products[handle]

    return new Promise((res, rej) => {

      product.variants = Object.values(products).map(p => p.variants.map(v => {
        v.product = p.handle
        return v;
      })).flat()

      res(product.variants)

    })

  },


  request: (product, variant) => {  
    Util.events.dispatch('Products:request', {product:product,variant:variant})
    return true
  }
}

window.Products = Products

window.addEventListener('DOMContentLoaded', () => {
  setTimeout(()=>{
    Object.keys(products).forEach(h=>Products.init(h))    
  },20)
})

window.products = window.products || {}

export default Products
